---
description: 
globs: 
alwaysApply: false
---
# Unity 资源管理规范

## 资源系统
- 项目统一使用 YooAsset 进行资源管理和异步加载。
- 资源加载应优先使用异步加载方式，避免阻塞主线程。

## 异步操作
- 异步操作统一使用 `async/await` 和 `UniTask` 进行处理。
- 加载资源后需检查 `op.Status`，确保资源加载成功。

## 资源生命周期
- 确保资源的正确加载、使用和释放，避免资源泄漏。
- 通过句柄（handle）管理资源，在不需要时调用 `handle.Release()` 释放。

## 典型用法
- 异步加载：`var handle = YooAssets.LoadAssetAsync<GameObject>("prefab_name");`
- 结合UniTask：`await handle.ToUniTask();`

---
详细实现见项目内 YooAsset 相关用法。

