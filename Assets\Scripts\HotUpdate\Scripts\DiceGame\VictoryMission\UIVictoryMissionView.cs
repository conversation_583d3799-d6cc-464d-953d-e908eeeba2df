using System;
using System.Collections.Generic;
using UnityEngine.UI;
using UnityEngine;
using TMPro;
using System.Text;
using CrazyCube.Protobuf.ActivityService;
using TKFrame;
using Google.Protobuf.Collections;
using CrazyCube.Protobuf.Public;
using DG.Tweening;
using Best.HTTP.SecureProtocol.Org.BouncyCastle.Asn1.Cms;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using System.Threading.Tasks;
using CrazyCube.Utils;

#if GAME_DICE

namespace SkierFramework
{
    public class UIVictoryMissionView : UIView
    {
        #region 控件绑定变量声明，自动生成请勿手改
#pragma warning disable 0649
		[ControlBinding]
		public Button CloseBtn;
        [ControlBinding]
        public Text CounterText;
        [ControlBinding]
        public GameObject TaskScrollView;
        [ControlBinding]
        public Transform GrandPrizeRewardNode;
        [ControlBinding]
        public GameObject MissionNode;
        [ControlBinding]
        public Text MissionTxt;
        [ControlBinding]
        public Image MissionIcon;
        [ControlBinding]
        public Transform MissionRewardNode;
        [ControlBinding]
        public Slider MissionProgressSlider;
        [ControlBinding]
        public Text MissionProgressTxt;
        [ControlBinding]
        public Button TipsBtn;
        [ControlBinding]
        public Button TipsNodeCloseBtn;
        [ControlBinding]
        public Transform TipsNode;
        [ControlBinding]
        public Text MissionTimeTxt;
        [ControlBinding]
        public GameObject SettlementNode;
        [ControlBinding]
        public Text SettlementTxt;
        [ControlBinding]
        public Button ContinueBtn;
        [ControlBinding]
        public Text ContinueBtnTxt;
        [ControlBinding]
        public Button RestBtn;
        [ControlBinding]
        public Text RestBtnTxt;
        [ControlBinding]
        public Animation SwitchAnimNode;
        [ControlBinding]
        public Button ResetTaskBtn;
        [ControlBinding]
        public Text ResetTaskTxt;
        [ControlBinding]
        public GameObject RedMan;
        [ControlBinding]
        public Transform Tick;
        [ControlBinding]
        public Transform Content;
        [ControlBinding]
        public Button TaskBtn;
        [ControlBinding]
        public Transform RedManParent;
        [ControlBinding]
        public GameObject Hook;

#pragma warning restore 0649
#endregion

        private VictoryMissionScrollView _victoryMissionScrollView;
        private VictoryMissionController _victoryMissionController => ControllerManager.Get<VictoryMissionController>();
        private PropController _propController => ControllerManager.Get<PropController>();
        private RedDotController _redDotController => ControllerManager.Get<RedDotController>();
        private StringBuilder _sb = new StringBuilder();
        public ulong timerId { get; set; }
        private bool isTask = false;

        //当前界面展示的任务配置
        private MultiWinningMissionsStageBaseTaskConf _showTaskConfig;
        //当前界面展示的任务进度
        private long _showTaskFinishCount;
        public static event Action TaskCloseAction;
        private List<GameObject> _grandPrizeHookList = new List<GameObject>();
        bool isProgressMax = false;


        public override void OnInit(UIControlData uIControlData, UIViewController controller)
        {
            base.OnInit(uIControlData, controller);
            TaskBtn.AddAntiFrequentClick(OnTaskClick);
            CloseBtn.AddAntiFrequentClick(OnCloseBtnClick);
            TipsBtn.AddAntiFrequentClick(OnTipsBtnClick);
            TipsNodeCloseBtn.AddAntiFrequentClick(OnTipsNodeClose);
            RestBtn.AddAntiFrequentClick(OnRestBtnClick);
            ContinueBtn.AddAntiFrequentClick(OnContinueBtnClick);
            ResetTaskBtn.AddAntiFrequentClick(ResetTaskTime);
        }

        public override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            _victoryMissionScrollView = TaskScrollView.GetComponent<VictoryMissionScrollView>();
            UpdateTaskScrollView();
            SetGrandPrizeReward();
            if(userData != null)
            {
                isTask = (bool)userData;
                Log.Info($"OnOpen isTask: {isTask}");
            }

            InitMissionNode();
            
            TaskCloseAction = null;
            isProgressMax = false;
        }

        public override void OnAddListener()
        {
            base.OnAddListener();
            Listen<string>(EventType.Activity.OnActivityBtnTimeDown, OnActivityOffline);
            Listen<int, Transform>(EventType.VictoryMission.RedManFly, OnRedManFly);
            Listen<int, Transform>(EventType.VictoryMission.UpdateRedManParent, OnUpdateRedManParent);
            Listen(EventType.VictoryMission.OnGetLastStageReward, OnGetLastStageReward);
            Listen<string>(EventType.Activity.OnActivityOffline, OnActivityOffline);
        }

        public override void OnRemoveListener()
        {
            base.OnRemoveListener();
        }

        //刷新滑动列表
        private async void UpdateTaskScrollView()
        {
            Log.Info($"UpdateTaskScrollView CurRoundIndex: {_victoryMissionController.CurRoundIndex}");
            Log.Info($"UpdateTaskScrollView Stages count: {_victoryMissionController.GetAllStageData().Count}");
            MultiWinningMissionsRoundConfig roundConfig = _victoryMissionController.Config.Rounds[_victoryMissionController.CurRoundIndex - 1];
            Log.Info($"UpdateTaskScrollView roundConfig.Stages.Count: {roundConfig.Stages.Count}");
            Log.Info($"UpdateTaskScrollView UserData.ThisStage: {_victoryMissionController.UserData.ThisStage}");
            //为了适配UI错位问题，第一个是占位，所以需要-1
            int initIndex = _victoryMissionController.GetAllStageData().Count - (int)_victoryMissionController.UserData.ThisStage - 1;
            Log.Info($"UpdateTaskScrollView initIndex: {initIndex}");
            _victoryMissionScrollView.Init(_victoryMissionController.GetAllStageData().Count, initIndex);
            Log.Info($"UpdateTaskScrollView Content.childCount: {Content.childCount}");
            //SetRedManPrarent((int)_victoryMissionController.UserData.ThisStage);     
        }

        private void InitMissionNode()
        {
            Log.Info($"InitMissionNode IsTaskTimeEnd: {_victoryMissionController.IsTaskTimeEnd()} IsFinishTask: {_victoryMissionController.IsFinishTask((int)_victoryMissionController.UserData.ThisTaskIndex)}");
            if (_victoryMissionController.IsTaskTimeEnd() || _victoryMissionController.IsFinishTask((int)_victoryMissionController.UserData.ThisTaskIndex))
            {
                Log.Info($"SettlementNodeAnim 01");
                SetSettlementNode();
                SettlementNode.SetActive(true);
                MissionNode.SetActive(false);
            }
            else
            {
                SetMissonNode();
                MissionNode.SetActive(true);
                SettlementNode.SetActive(false);
                CheckMissionNodeProgress();
            }
        }

        private void SetGrandPrizeReward()
        {
            RepeatedField<PrizeShow> lastStageReward = new RepeatedField<PrizeShow>();//最后一个阶段的奖励
            RepeatedField<MultiWinningMissionsRateBaseConf> stages = _victoryMissionController.Config.Rounds[_victoryMissionController.CurRoundIndex - 1].Stages;
            Log.Info($"SetGrandPrizeReward Stages: {stages}");
            lastStageReward = stages[stages.Count - 1].Prize;
            PropSetting setting = new PropSetting();
            _propController.UpdateProps(lastStageReward, 0.35f, GrandPrizeRewardNode, setting, true, cb: (propBase) =>
            {
                Log.Info($"SetGrandPrizeReward Creat Prop Finish By RewardNode");
            });
            
            bool isAllFinish = _victoryMissionController.UserData.RcRate.Count == _victoryMissionController.CurStageIndex;
            Log.Info($"SetGrandPrizeReward isAllFinish: {isAllFinish}");
            LoadHook(isAllFinish);
        }

        private void OnCloseBtnClick()
        {
            if(_victoryMissionController.isPlayingAnim)
                return;
            Dispose();
        }

        private void OnTipsBtnClick()
        {
            TipsNodeCloseBtn.gameObject.SetActive(true);
            TipsNode.DOScale(1f, 0.1f);
        }

        private void OnTipsNodeClose()
        {
            TipsNode.DOScale(0f, 0.1f);
            TipsNodeCloseBtn.gameObject.SetActive(false);
        }

        public void SetMissonNode()
        {
            //Log.Info($"SetMissonNode UserData.ThisTaskIndex: {_victoryMissionController.UserData.ThisTaskIndex}");
            long taskIdx = _victoryMissionController.UserData.ThisTaskIndex;
            _showTaskConfig = _victoryMissionController.GetTaskConfig();
            long taskId = _showTaskConfig.TaskId;     
            _showTaskFinishCount = _victoryMissionController.GetFinishTaskCount((int)taskIdx);

            //Log.Info($"SetMissonNode taskId: {taskId} langKey: {_victoryMissionController.GetTaskLangKey(taskId)}");
            MissionTxt.text = string.Format(LangManager.Instance.GetText(_victoryMissionController.GetTaskLangKey(taskId)), _showTaskConfig.MaxNum);

            //Log.Info($"SetMissonNode _victoryMissionController.GetTaskIconPath(taskId): {_victoryMissionController.GetTaskIconPath(taskId)}");
            ResUtil.SetDefaultImage(MissionIcon, _victoryMissionController.GetTaskIconPath(taskId), false);

            PropSetting setting = new PropSetting();
            RepeatedField<PrizeShow> _reward = _victoryMissionController.TaskReward();
            //Log.Info($"SetMissonNode _reward: {_reward}");
            _propController.UpdateProps(_reward, 0.37f, MissionRewardNode, setting, true, cb: (propBase) =>
            {

            });
            
            long taskFinishCount = _victoryMissionController.GetFinishTaskCount((int)taskIdx);

            //Log.Info($"SetMissonNode GetFinishTaskCount: {taskFinishCount} taskConfig.MaxNum: {_showTaskConfig.MaxNum}");
            MissionProgressTxt.text = $"{taskFinishCount}/{_showTaskConfig.MaxNum}";
            MissionProgressSlider.value = (float)taskFinishCount / _showTaskConfig.MaxNum;

            if(timerId != 0)
            {
                Timer.ClearTimer(timerId);
            }
            timerId = Timer.SetInterval(1, CountDown, true);


            //this.timerId = Timer.SetInterval(1f, CountDown, true);
            //Log.Info($"SetMissonNode IsTaskTimeEnd: {_victoryMissionController.IsTaskTimeEnd()}");
        }

        protected void CountDown()
        {
            long ts = TimeTool.GetCurCountdown(_victoryMissionController.GetTaskTimeEnd());
            if (ts <= 0)
            {
                Log.Info($"SettlementNodeAnim 03");
                SetSettlementNode();
                SettlementNode.SetActive(true);
                MissionNode.SetActive(false);
            }
            string txt = ActivityController.GetCountDownTxt(ts);
            if (!MissionTimeTxt.IsDestroyed())
            {
                MissionTimeTxt.text = txt;
            }
        }

        private void SetSettlementNode(bool isGetReward = false)
        {
            // Log.Info($"SetSettlementNode isGetReward: {isGetReward} IsTaskTimeEnd: {_victoryMissionController.IsTaskTimeEnd()} IsFinishTask: {_victoryMissionController.IsFinishTask((int)_victoryMissionController.UserData.ThisTaskIndex) } GetTaskTimeEnd: {_victoryMissionController.GetTaskTimeEnd()}");
            if(isGetReward)
            {
                SettlementTxt.text = LangManager.Instance.GetText("VICTORY_DASH.TEXT2");
                ContinueBtn.gameObject.SetActive(false);
                ResetTaskBtn.gameObject.SetActive(true);
            }
            //有大于0且小于当前时间才算任务失败，如果为0，则表示没有在做任务
            else if(_victoryMissionController.IsTaskTimeEnd() && _victoryMissionController.GetTaskTimeEnd() != 0)
            {
                SettlementTxt.text = LangManager.Instance.GetText("VICTORY_DASH.TEXT3");
                ContinueBtn.gameObject.SetActive(true);
                ResetTaskBtn.gameObject.SetActive(false);
            }
            else if(_victoryMissionController.GetTaskTimeEnd() == 0)
            {
                SettlementTxt.text = LangManager.Instance.GetText("VICTORY_DASH.TEXT2");
                ContinueBtn.gameObject.SetActive(false);
                ResetTaskBtn.gameObject.SetActive(true);
            }

            ContinueBtnTxt.text = LangManager.Instance.GetText("VICTORY_DASH.CONTINUE");
            RestBtnTxt.text = LangManager.Instance.GetText("VICTORY_DASH.REST");
            ResetTaskTxt.text = LangManager.Instance.GetText("VICTORY_DASH.CONTINUE");
        }

        private void OnRestBtnClick()
        {
            Dispose();
        }

        private void OnContinueBtnClick()
        {
            bool isAllFinish = _victoryMissionController.UserData.RcRate.Count == _victoryMissionController.CurStageIndex;
            _victoryMissionController.VictoryMissionsInitUserTask(async () =>
            {
                Log.Info($"ContinueMission ContinueMission");
                Log.Info($"连胜任务 VictoryMissionsSetUserActStatus 01");
                _victoryMissionController.VictoryMissionsSetUserActStatus(async (response) =>
                {
                    Log.Info($"ContinueMission ContinueMission isAllFinish: {isAllFinish}");
                    await UniTask.NextFrame();
                    Trigger(EventType.VictoryMission.ResetStageProgress, _victoryMissionController.CurStageIndex);
                    await ContinueMission();
                });
            });
        }

        private void ResetTaskTime()
        {
            Log.Info($"连胜任务 VictoryMissionsSetUserActStatus 02");
            _victoryMissionController.VictoryMissionsSetUserActStatus(async (response) =>
            {
                await UniTask.NextFrame();
                await ContinueMission();
            });
        }


        private async UniTask ContinueMission()
        {
            SetGrandPrizeReward();
            SetMissonNode();
            Log.Info($"ContinueMission MissionNodeAnim");
            await MissionNodeAnim();
        }

        private async UniTask MissionNodeAnim()
        {
            AnimTools.ResetAnimation(SwitchAnimNode);
            await AnimTools.Play(SwitchAnimNode, "am_qiehuan02");
        }

        private async UniTask SettlementNodeAnim()
        {
            AnimTools.ResetAnimation(SwitchAnimNode);
            await AnimTools.Play(SwitchAnimNode, "am_qiehuan01");
            // SettlementNode.SetActive(true);
            // MissionNode.SetActive(false);
        }

        private async UniTask CheckMissionNodeProgress()
        {
            await UniTask.WaitForSeconds(1f);
            _victoryMissionController.UpdateUserData(async () =>
            {
                long taskFinishCount = _victoryMissionController.GetFinishTaskCount((int)_victoryMissionController.UserData.ThisTaskIndex);
                Log.Info($"CheckMissionNodeProgress taskFinishCount: {taskFinishCount} _showTaskFinishCount: {_showTaskFinishCount} showTaskConfig.MaxNum: {_showTaskConfig.MaxNum}");
                if (_showTaskFinishCount < taskFinishCount)
                {
                    PlayMissionNodeProgressAnim(_showTaskFinishCount, taskFinishCount, _showTaskConfig.MaxNum);
                }
            }, () =>
            {

            });
        }

        /// <summary>
        /// 任务栏进度条动画
        /// </summary>
        /// <param name="taskFinishCountBefore"></param>
        /// <param name="taskFinishCount"></param>
        /// <param name="maxNum"></param>
        /// <returns></returns>
        private async UniTask PlayMissionNodeProgressAnim(float taskFinishCountBefore, float taskFinishCount, float maxNum)
        {
            _victoryMissionController.isPlayingAnim = true;
            float end = Mathf.Min(taskFinishCount, maxNum);
            Log.Info($"PlayMissionNodeProgressAnim end: {end} maxNum: {maxNum}");
            MissionProgressSlider.DOValue(end / maxNum, 0.5f).onUpdate = () =>
            {
                MissionProgressTxt.text =   Mathf.CeilToInt(end).ToString() + "/" + maxNum;
            };

            await UniTask.Delay(510); //等待进度条更新完毕

            if (taskFinishCount >= maxNum)
            {
                isProgressMax = true;
                _victoryMissionController.GetVictoryMissionReward((int)_victoryMissionController.UserData.ThisTaskIndex, async (response) =>
                {
                    Log.Info($"PlayMissionNodeProgressAnim GetVictoryMissionReward");
                    Log.Info($"PlayMissionNodeProgressAnim _victoryMissionController.CurStageIndex: {_victoryMissionController.CurStageIndex} CurGotRewardTaskCount: {_victoryMissionController.CurGotRewardTaskCount}");

                    //清除倒计时
                    if(timerId != 0)
                    {
                        Timer.ClearTimer(timerId);
                    }

                    await GetRewardAnim();
                    await UniTask.Delay(200);

                    Trigger(EventType.VictoryMission.StageProgressAdd, _victoryMissionController.CurStageIndex, 1);

                    await UniTask.Delay(500);
                    Log.Info($"SettlementNodeAnim 02");
                    bool isLastStage = _victoryMissionController.CurStageIndex == _victoryMissionController.GetAllStageData().Count - 2;
                    bool isAllFinish = _victoryMissionController.UserData.RcRate.Count == _victoryMissionController.UserData.ThisTaskIndex;
                    Log.Info($"PlayMissionNodeProgressAnim isLastStage: {isLastStage} isAllFinish: {isAllFinish}");
                    //完成最后一个阶段的最后一个任务，已经通关，会直接关闭界面 不需要再处理下面的逻辑了
                    if(isLastStage && isAllFinish)
                    {
                        return;
                    }

                    SetSettlementNode(true);
                    await SettlementNodeAnim();
                    ResetTick();

                    _redDotController.SetRedDotStatus(RedDotKey.Activity_VictoryMissionBtn, 0);

                    isProgressMax = false;
                }, fail: () =>
                {
                    _victoryMissionController.isPlayingAnim = false;
                });
            }
            else
            {
                _victoryMissionController.isPlayingAnim = false;
            }
        }

        public void RedManIdelAnim()
        {
            SpineTool.PlaySpineAnimAsync(RedMan, "wait", true);
        }

        public void RedManFlyAnim()
        {
            SpineTool.PlaySpineAnimAsync(RedMan, "shang", false);
        }

        public async UniTask RedManFly(Transform redManParent)
        {
            ResetRedManPrarent();
            await UniTask.WaitForSeconds(0.1f);
            RedManFlyAnim();
            int t = 0;
            if((int)_victoryMissionController.UserData.ThisStage == 2)
            {
                t = 1;
            }
            //为了适配UI错位问题，第一个是占位，所以需要-1
            int moveIndex = _victoryMissionController.GetAllStageData().Count - (int)_victoryMissionController.UserData.ThisStage - 1 - t;
            Log.Info($"RedManFly moveIndex: {moveIndex} t: {t}");
            _victoryMissionScrollView.OnMove(moveIndex);
            //await DecreaseContentYValueAlternative();
            //
            RedManIdelAnim();
            await UniTask.WaitForSeconds(1.5f);
            _victoryMissionController.isPlayingAnim = false;
            SetRedManPrarent(_victoryMissionController.CurStageIndex);
            //OnUpdateRedManParent(_victoryMissionController.CurStageIndex, redManParent);
            await UniTask.WaitForSeconds(0.5f);
            //Dispose();
        }
        private async UniTask DecreaseContentYValueAlternative(float decreaseValue = 267f, float duration = 1.5f)
        {
            RectTransform contentRectTransform = Content.GetComponent<RectTransform>();
            if (contentRectTransform != null)
            {
                Vector2 currentPos = contentRectTransform.anchoredPosition;
                float startY = currentPos.y;
                float endY = startY - decreaseValue;
                
                await DOTween.To(() => startY, (y) => {
                    Vector2 newPos = contentRectTransform.anchoredPosition;
                    newPos.y = y;
                    contentRectTransform.anchoredPosition = newPos;
                }, endY, duration);
            }
        }

        private void OnRedManFly(int stageId, Transform redManParent)
        {
            Log.Info($"OnRedManFly stageId: {stageId}");

            Vector3 targetPos = Vector3.zero;
            // for(int i = 0; i < _stageItems.Count; i++)
            // {
            //     if(_stageItems[i].StageData.Stage == stageId)
            //     {
            //         targetPos = _stageItems[i].transform.position;
            //     }
            // }
            Log.Info($"OnRedManFly targetPos: {targetPos}");
            RedManFly(redManParent);
        }


        private void SetRedManPrarent(int stageId, bool isOnOpen = false)
        {
            Transform redManParent = null;
            Log.Info($"SetRedManPrarent stageId: {stageId} Content.childCount: {Content.childCount}");
            for(int i = 0; i < Content.childCount; i++)
            {
                Log.Info($"SetRedManPrarent Content.GetChild(i).name: {Content.GetChild(i).name} IsNullItem: {Content.GetChild(i).GetComponent<VictoryMissionStageItem>().IsNullItem}");
                VictoryMissionStageItem item = Content.GetChild(i).GetComponent<VictoryMissionStageItem>();
                if(item.IsNullItem)
                {
                    continue;
                }
                if(item.StageData == null)
                {
                    continue;
                }
                Log.Info($"SetRedManPrarent Content.GetChild(i).name {Content.GetChild(i).name} item.StageData.Stage: {item.StageData.Stage}");
                if(stageId == (int)item.StageData.Stage)
                {
                    Log.Info($"SetRedManPrarent item.RedManParent: {item.RedManParent.parent.name}");
                    redManParent = item.RedManParent;
                    RedMan.gameObject.SetActive(true);
                }
            }
            Log.Info($"SetRedManPrarent redManParent edManParent != null: {redManParent != null}");
            if(redManParent != null)
            {
                Log.Info($"SetRedManPrarent redManParent: {redManParent.parent.name}");
                RedMan.transform.SetParent(redManParent);
                if(isOnOpen)
                {
                    RedMan.transform.localPosition = Vector3.zero;
                }
            }
            RedMan.transform.localScale = Vector3.one;
        }

        private void ResetRedManPrarent()
        {
            RedMan.transform.SetParent(RedManParent);
        }

        private void OnUpdateRedManParent(int stageId, Transform redManParent)
        {
            Log.Info($"OnUpdateRedManParent stageId: {stageId} redManParent: {redManParent.parent.name}");
            RedMan.transform.SetParent(redManParent);
            RedMan.transform.localScale = Vector3.one;
            RedMan.transform.localPosition = Vector3.zero;
        }

        private async UniTask GetRewardAnim()
        {
            await Tick.DOScale(1f, 0.3f);
        }

        private void ResetTick()
        {
            Tick.localScale = Vector3.zero;
        }

        private void OnActivityOffline(string actType)
        {
            if(actType == ActType.VictoryMission)
                Dispose();
        }

        //点击任务跳转
        private void OnTaskClick()
        {
            if(_victoryMissionController.isPlayingAnim)
                return;

            TaskCloseAction = null;
            TaskCloseAction = _victoryMissionController.TaskClickAction(_showTaskConfig.TaskId);
            Dispose();
        }

        public override void OnClose()
        {
            if(isTask)
            {
                ControllerManager.Get<TaskController>().Done(WaitingTaskType.VictoryMissionTask);
            }
            Log.Info($"OnClose _victoryMissionController.BagReward.Count: {_victoryMissionController.BagReward.Count}");
            if(_victoryMissionController.BagReward.Count > 0)
            {
                _propController.PlayReward(_victoryMissionController.BagReward, () =>
                {
                    _victoryMissionController.BagReward.Clear();
                    TaskCloseAction?.Invoke();
                });
            }
            else
            {
                TaskCloseAction?.Invoke();
            }


            _victoryMissionController.isPlayingAnim = false;
            _victoryMissionController.UpdateUserData();
            Trigger(EventType.VictoryMission.UpdateBtnIcon, false);

            if(isProgressMax)
            {
                Log.Info($"连胜任务 VictoryMissionsSetUserActStatus 03");
                _victoryMissionController.VictoryMissionsSetUserActStatus(async (response) =>
                {

                });
            }

            base.OnClose();
        }

        private async void LoadHook(bool isFinish)
        {
            _grandPrizeHookList.Clear();
            await UniTask.Delay(20);
            for (int i = 0; i < GrandPrizeRewardNode.childCount; i++)
            {
                GameObject hook = Instantiate(Hook, GrandPrizeRewardNode.GetChild(i));
                hook.transform.localPosition = Vector3.zero;
                hook.transform.localScale = new Vector3(2.5f, 2.5f, 2.5f);
                hook.SetActive(false);
                _grandPrizeHookList.Add(hook);
                if (isFinish)
                {
                    hook.SetActive(true);
                }
            }
        }

        private void PlayGrandPrizeHookAnim()
        {
            for (int i = 0; i < _grandPrizeHookList.Count; i++)
            {
                AnimTools.Play(_grandPrizeHookList[i].GetComponent<Animation>(), "am_dagou");
                _grandPrizeHookList[i].SetActive(true);
            }
        }

        private async void OnGetLastStageReward()
        {
            Log.Info($"OnGetLastStageReward");
            PlayGrandPrizeHookAnim();
            await UniTask.WaitForSeconds(1.5f);
            Trigger(EventType.VictoryMission.HideBtn);
            Dispose();
        }
    }
}
#endif