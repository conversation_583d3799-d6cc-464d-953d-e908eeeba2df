---
description: 
globs: 
alwaysApply: true
---
# Unity 项目规范索引

## 1. 核心架构与模块
- **项目架构**: 见 [unity-architecture-overview.mdc](mdc:.cursor/rules/unity-architecture-overview.mdc)
- **控制器规范**: 见 [unity-controller-standard.mdc](mdc:.cursor/rules/unity-controller-standard.mdc)
- **UI行为规范**: 见 [unity-ui-behavior.mdc](mdc:.cursor/rules/unity-ui-behavior.mdc)

## 2. 核心系统规范
- **事件系统**: 见 [unity-event-system.mdc](mdc:.cursor/rules/unity-event-system.mdc)
- **日志系统**: 见 [unity-logging.mdc](mdc:.cursor/rules/unity-logging.mdc)
- **本地化系统**: 见 [unity-localization.mdc](mdc:.cursor/rules/unity-localization.mdc)

## 3. 业务与数据
- **资源管理**: 见 [unity-resource-management.mdc](mdc:.cursor/rules/unity-resource-management.mdc)
- **数据与配置**: 见 [unity-data-config.mdc](mdc:.cursor/rules/unity-data-config.mdc)
- **网络通信**: 见 [unity-networking.mdc](mdc:.cursor/rules/unity-networking.mdc)

## 4. 质量与部署
- **代码质量与构建**: 见 [unity-code-quality.mdc](mdc:.cursor/rules/unity-code-quality.mdc)

---
这是一个模块化的规则索引。请查阅链接的各规则文件以获取详细信息。

