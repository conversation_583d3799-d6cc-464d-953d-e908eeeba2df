using CrazyCube.Protobuf.Public;
using CrazyCube.Protobuf.SlotService;
using Google.Protobuf;
using System.Collections.Generic;
using System.Text;
using TKFrame;
using UnityEngine;
using Best.HTTP.JSON.LitJson;
using static FreerollConfig;
using System;
using Google.Protobuf.Collections;
using SkierFramework;
using Cysharp.Threading.Tasks;
using YooAsset;
using CrazyCube.Protobuf.ActivityService;

#if GAME_DICE

public partial class VictoryMissionController //字段和获取数据
{
    public virtual string actType => ActType.VictoryMission;
    public virtual UIType uIType => UIType.VictoryMissionView;
    public virtual RedDotKey redDotKey => RedDotKey.Activity_VictoryMissionBtn;
    //public virtual string slotEvent => EventType.SLOT.ResultVictoryMission;
    private UserController _userController = ControllerManager.Get<UserController>();
    private ActivityController _activityController => ControllerManager.Get<ActivityController>();
    protected RedDotController _redDotController => ControllerManager.Get<RedDotController>();
    protected PropController _propController => ControllerManager.Get<PropController>();
    public RepeatedField<PrizeReward> BagReward = new RepeatedField<PrizeReward>();
    public RepeatedField<PrizeReward> ReissueReward = new RepeatedField<PrizeReward>();
    //是否正在播放动画
    public bool isPlayingAnim = false;
    //是否可以在任务失败的时候自动打开
    public bool isCanFailAutoOpen = false;

    /// <summary>
    /// 是否初始化任务
    /// </summary>
    public bool IsInitTask = false;


    public ActivityInfo ActivityInfo
    {
        get
        {
            return _activityController.TryGetActInfoByType(actType);
        }
    }

    private MultiWinningMissionsUserData _userData;
    /// <summary>
    /// 玩家连胜任务数据
    /// </summary>
    public MultiWinningMissionsUserData UserData
    {
        get
        {
            return _userData;
        }
    }

    private MultiWinningMissionsConfig _config;
    /// <summary>
    /// 连胜任务配置
    /// </summary>
    public MultiWinningMissionsConfig Config
    {
        get
        {
            return _config;
        }
    }

    /// <summary>
    /// 玩家当前轮次
    /// </summary>
    public int CurRoundIndex
    {
        get
        {
            return (int)_userData.CurRound;
        }
    }

    /// <summary>
    /// 玩家当前阶段
    /// </summary>
    public int CurStageIndex
    {
        get
        {
            return (int)_userData.ThisStage;
        }
    }


    /// <summary>
    /// 玩家所处阶段已经完成的任务数量
    /// </summary>
    public int CurFinishTaskCount
    {
        get
        {
            return (int)_userData.CurFinishStage.Count;
        }
    }

    /// <summary>
    /// 玩家所处阶段已经领奖的任务的数量
    /// </summary>
    public int CurGotRewardTaskCount
    {
        get
        {
            return (int)_userData.CurRate;
        }
    }

    /// <summary>
    /// 此阶段是否已经领取奖励
    /// </summary>
    public bool IsGotRewardStage(int stage)
    {
        bool isGotReward = false;
        foreach (var item in _userData.RcStage)
        {
            if(item == stage)
            {
                isGotReward = true;
                break;
            }
        }
        return isGotReward;
    }

    /// <summary>
    /// 此阶段是否完成
    /// </summary>
    /// <param name="stage"></param>
    /// <returns></returns>
    public bool IsFinishStage(int stage)
    {
        Log.Info($"VictoryMissionController.IsFinishStage _userData.ThisStage: {_userData.ThisStage} stage: {stage}");
        bool isFinish = false;
        if(_userData.ThisStage > stage)
        {
            isFinish = true;
        }
        return isFinish;
    }

    /// <summary>
    /// 判断是否是最后一阶段
    /// </summary>
    /// <param name="stage"></param>
    /// <returns></returns>
    public bool IsMaxStage(int stage)
    {
        return stage == _config.Rounds[CurRoundIndex - 1].Stages.Count;
    }

    /// <summary>
    /// 判断任务是否完成
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public bool IsFinishTask(int taskIdx)
    {
        long taskFinishCount = _userData.CurStage.TryGetValue(taskIdx, out long value) ? value : 0;
        long maxNum = GetTaskConfig().MaxNum;
        return taskFinishCount >= maxNum;
    }

    /// <summary>
    /// 获取此阶段任务数量
    /// </summary>
    /// <param name="stage"></param>
    /// <returns></returns>
    public int GetStageTaskCount(int stage)
    {
        MultiWinningMissionsRateBaseConf stageData = GetStageData(stage);
        return (int)stageData.TaskRounds[GetTaskRoundIdx()].Tasks.Count;
    }

    public int CurFailTaskCount
    {
        get
        {
            return (int)_userData.FailedTimes;
        }
    }

    public override void Offline()
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 获取任务语言key
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    public string GetTaskLangKey(long taskId)
    {
        if (!_taskIdToLangKey.ContainsKey(taskId))
        {
            return "";
        }
        return _taskIdToLangKey[taskId];
    }

    private Dictionary<long, string> _taskIdToLangKey = new Dictionary<long, string>
    {
        //偷次数
        { 2, "GUIDE_BOOK.MISSION9" },
        //占领次数
        { 3, "GUIDE_BOOK.MISSION10" },
        //获取卡片数
        { 4, "GUIDE_BOOK.MISSION11" },
        //登录天数 
        { 5, "MISSON.TEXT5" },
        //投掷骰子次数
        { 7, "MISSON.TEXT7" },
        //锦标赛排名前3
        { 8, "MISSON.TEXT8" },
        //锦标赛第1名
        { 9, "MISSON.TEXT9" },
        //获取彩虹卡次数
        { 10, "MISSON.TEXT10" },
        //使用小丑卡次数
        { 11, "MISSON.TEXT11" },
        //邀请好友
        { 12, "MISSON.TEXT12" },
        //收集金卡数
        { 14, "MISSON.TEXT14" },
        //帮助好友刮卡次数
        { 16, "MISSON.TEXT16" },
        //亲密度增加值
        { 17, "MISSON.TEXT17" },
        //付费次数
        { 19, "MISSON.TEXT19" },
        //幸运冲刺代币使用次数
        { 20, "MISSON.TEXT20" },
        //锦标赛排名前5
        { 21, "MISSON.TEXT21" },
        //锦标赛排名前10
        { 22, "MISSON.TEXT22" },
        //锦标赛排名前20
        { 23, "MISSON.TEXT23" },
        //锦标赛排名前30
        { 24, "MISSON.TEXT24" },
        //获得盾牌数量
        { 25, "MISSON.TEXT25" },
        //分享
        { 26, "MISSON.TEXT26" },
        //疯狂游客次数
        { 2007, "GUIDE_BOOK.MISSION14" },
    };

    /// <summary>
    /// 获取任务图标资源路径
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    public string GetTaskIconPath(long taskId)
    {
        if (!_taskIdToIconPath.ContainsKey(taskId))
        {
            return "";
        }
        return _taskIdToIconPath[taskId];
    }

    private Dictionary<long, string> _taskIdToIconPath = new Dictionary<long, string>
    {
        //偷次数
        { 2, "TaskIcon_img_daily_heist" },
        //占领次数
        { 3, "TaskIcon_img_daily_demolish" },
        //获取卡片数
        { 4, "TaskIcon_img_daily_sticker" },
        //登录天数 
        { 5, "TaskIcon_img_daily_calendar" },
        //投掷骰子次数
        { 7, "TaskIcon_img_daily_spins" },
        //锦标赛排名前3
        { 8, "TaskIcon_img_daily_tournament_three" },
        //锦标赛第1名
        { 9, "TaskIcon_img_daily_tournament_one" },
        //获取彩虹卡次数
        { 10, "TaskIcon_img_daily_rainbow" },
        //使用小丑卡次数
        { 11, "TaskIcon_img_daily_joker" },
        //邀请好友
        { 12, "TaskIcon_img_daily_invite" },
        //收集金卡数
        { 14, "TaskIcon_img_daily_golden" },
        //帮助好友刮卡次数
        { 16, "TaskIcon_img_daily_scratch" },
        //亲密度增加值
        { 17, "TaskIcon_img_daily_friendship" },
        //付费次数
        { 19, "TaskIcon_img_daily_shop" },
        //幸运冲刺代币使用次数
        { 20, "TaskIcon_img_daily_lucky" },
        //锦标赛排名前5
        { 21, "TaskIcon_img_daily_tournament_five" },
        //锦标赛排名前10
        { 22, "TaskIcon_img_daily_tournament_ten" },
        //锦标赛排名前20
        { 23, "TaskIcon_img_daily_tournament_twenty" },
        //锦标赛排名前30
        { 24, "TaskIcon_img_daily_tournament_thirty" },
        //获得盾牌数量
        { 25, "TaskIcon_img_daily_shield" },
        //分享
        { 26, "TaskIcon_img_daily_share" },
        //疯狂游客次数
        { 2007, "TaskIcon_img_daily_tourist" },
    };

    /// <summary>
    /// 玩家当前任务的奖励
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public RepeatedField<PrizeShow> TaskReward()
    {
        return _config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds[GetTaskRoundIdx()].Tasks[(int)UserData.ThisTaskIndex - 1].Prize;
    }

    /// <summary>
    /// 获取当前做的任务id
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public long GetTaskData()
    {
        return _config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds[GetTaskRoundIdx()].Tasks[(int)UserData.ThisTaskIndex - 1].TaskId;
    }

    /// <summary>
    /// 获取当前做的任务配置
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public MultiWinningMissionsStageBaseTaskConf GetTaskConfig()
    {
        Log.Info($"VictoryMissionController.GetTaskConfig CurRoundIndex: {CurRoundIndex} CurStageIndex: {CurStageIndex} GetTaskRoundIdx: {GetTaskRoundIdx()} UserData.ThisTaskIndex: {UserData.ThisTaskIndex}");
        //失败次数大于任务轮次，则取最后一个任务轮次
        return _config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds[GetTaskRoundIdx()].Tasks[(int)UserData.ThisTaskIndex - 1];
    }

    /// <summary>
    /// 获取当前做的任务完成数量
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public long GetFinishTaskCount(int taskIdx)
    {
        return _userData.CurStage.TryGetValue(taskIdx, out long value) ? value : 0;
    }

    /// <summary>
    /// 获取当前任务轮次，失败次数大于任务轮次，则取最后一个任务轮次
    /// </summary>
    /// <returns></returns>
    public int GetTaskRoundIdx()
    {
        Log.Info($"VictoryMissionController.GetTaskRoundIdx CurFailTaskCount: {CurFailTaskCount} _config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds.Count: {_config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds.Count}");
        return CurFailTaskCount > (_config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds.Count - 1)
         ? (_config.Rounds[CurRoundIndex - 1].Stages[CurStageIndex - 1].TaskRounds.Count - 1) : CurFailTaskCount;
    }

    /// <summary>
    /// 当前轮次阶段列表（反转后）
    /// </summary>
    public RepeatedField<MultiWinningMissionsRateBaseConf> ReversedStages
    {
        get
        {
            if (_config != null && _config.Rounds != null && CurRoundIndex > 0 && CurRoundIndex <= _config.Rounds.Count)
            {
                var currentRound = _config.Rounds[CurRoundIndex - 1];
                if (currentRound.Stages != null && currentRound.Stages.Count > 0)
                {
                    var reversedStages = new RepeatedField<MultiWinningMissionsRateBaseConf>
                    {
                        // 在前一个位置添加空数据作为占位
                        new MultiWinningMissionsRateBaseConf()
                    };
                    // 反转Stages列表
                    for (int i = currentRound.Stages.Count - 1; i >= 0; i--)
                    {
                        reversedStages.Add(currentRound.Stages[i]);
                    }
                    // 在末尾添加空数据作为占位
                    reversedStages.Add(new MultiWinningMissionsRateBaseConf());
                    return reversedStages;
                }
            }
            return new RepeatedField<MultiWinningMissionsRateBaseConf>();
        }
    }

    /// <summary>
    /// 获取所有阶段的数据，滑动列表是从上往下创建的，如果数组是正序的，则需要反转，首位两个元素是空的占位元素
    /// </summary>
    /// <returns></returns>
    public RepeatedField<MultiWinningMissionsRateBaseConf> GetAllStageData()
    {
        if(_config.Rounds[CurRoundIndex - 1].Stages[0].Stage == 1)
        {
            return ReversedStages;
        }
        else
        {
            return _config.Rounds[CurRoundIndex - 1].Stages;
        }
    }

    /// <summary>
    /// 获取此阶段数据
    /// </summary>
    /// <param name="stage"></param>
    /// <returns></returns>
    public MultiWinningMissionsRateBaseConf GetStageData(int stage)
    {
        return _config.Rounds[CurRoundIndex - 1].Stages[stage - 1];
    }

    /// <summary>
    /// 判断任务时间是否结束
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public bool IsTaskTimeEnd()
    {
        Log.Info($"VictoryMissionController.IsTaskTimeEnd TimeTool.IsExpire(UserData.TaskEndTime): {TimeTool.IsExpire(UserData.TaskEndTime)}");
        return TimeTool.IsExpire(UserData.TaskEndTime);
    }

    /// <summary>
    /// 获取任务时间结束时间
    /// </summary>
    /// <param name="taskIdx"></param>
    /// <returns></returns>
    public long GetTaskTimeEnd()
    {
        Log.Info($"VictoryMissionController.GetTaskTimeEnd UserData.TaskEndTime: {UserData.TaskEndTime}");
        return UserData.TaskEndTime;
    }

    
}

public partial class VictoryMissionController //http
{
    public override void OnConfigChange()
    {
        _activityController.RequestActConfigByActType<MultiWinningMissionsConfig>(actType, (config) =>
        {
            _config = config;
            Log.Info($"VictoryMissionController.TryInit config:{config}");
        });
        _activityController.RequestActUserDataByActType<MultiWinningMissionsUserData>(actType, (config) =>
        {
            Log.Info($"VictoryMissionController.TryInit userData:{config}");
            _userData = config;
            EventCenter.Instance.Event.TriggerEvent(EventType.VictoryMission.UpdateBtnIcon, false);
        });
    }

    /// <summary>
    /// 获取任务奖励
    /// </summary>
    public void GetVictoryMissionReward(int taskIdx, Action<ReceiveMultiWinningMissionsStageRewardResponse> suc = null, Action fail = null)
    {
        ReceiveMultiWinningMissionsStageRewardRequest request = new ReceiveMultiWinningMissionsStageRewardRequest();
        request.Uid = _userController.UID;
        request.TaskIndex = taskIdx;

        Log.Info($"VictoryMissionController.GetVictoryMissionReward url:{HttpServiceRoute.GetVictoryMissionRewardUrl} request:{request}");
        GatewayHttpService.PostWaiting(HttpServiceRoute.GetVictoryMissionRewardUrl, request.ToByteArray(), (status, resp) =>
        {
            if (status)
            {
                ReceiveMultiWinningMissionsStageRewardResponse response = ProtobufUtil.Parser<ReceiveMultiWinningMissionsStageRewardResponse>(resp, () => new ReceiveMultiWinningMissionsStageRewardResponse());
                Log.Info($"VictoryMissionController.GetVictoryMissionReward url:{HttpServiceRoute.GetVictoryMissionRewardUrl} response:{response}");
                UpdateUserDataTaskReward(response);
                suc?.Invoke(response);
            }
            else
            {
                var error = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"VictoryMissionController.GetVictoryMissionReward url:{HttpServiceRoute.GetVictoryMissionRewardUrl} error:{error}");
                fail?.Invoke();
            }
        }, null);
    }

    /// <summary>
    /// 领取进度奖励
    /// </summary>
    /// <param name="suc"></param>
    /// <param name="fail"></param>
    public void GetVictoryMissionRateReward(int stageId, Action<ReceiveMultiWinningMissionsRateRewardResponse> suc = null, Action fail = null)
    {
        ReceiveMultiWinningMissionsRateRewardRequest request = new ReceiveMultiWinningMissionsRateRewardRequest();
        request.Uid = _userController.UID;
        request.Stage = stageId;

        Log.Info($"VictoryMissionController.GetVictoryMissionRateReward url:{HttpServiceRoute.GetVictoryMissionRateRewardUrl} request:{request}");
        GatewayHttpService.PostWaiting(HttpServiceRoute.GetVictoryMissionRateRewardUrl, request.ToByteArray(), (status, resp) =>
        {
            if (status)
            {
                ReceiveMultiWinningMissionsRateRewardResponse response = ProtobufUtil.Parser<ReceiveMultiWinningMissionsRateRewardResponse>(resp, () => new ReceiveMultiWinningMissionsRateRewardResponse());
                Log.Info($"VictoryMissionController.GetVictoryMissionRateReward url:{HttpServiceRoute.GetVictoryMissionRateRewardUrl} response:{response}");
                UpdateUserDataStageRrward(response);
                suc?.Invoke(response);
            }
            else
            {
                var error = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"VictoryMissionController.GetVictoryMissionRateReward url:{HttpServiceRoute.GetVictoryMissionRateRewardUrl} error:{error}");
                fail?.Invoke();
            }
        }, null);
    }

    /// <summary>
    /// 初始化玩家连胜任务数据，任务失败时候调用
    /// </summary>
    /// <param name="suc"></param>
    /// <param name="fail"></param>
    public void VictoryMissionsInitUserTask(Action suc = null, Action fail = null)
    {
        MultiWinningMissionsInitUserTaskRequest request = new MultiWinningMissionsInitUserTaskRequest();
        request.Uid = _userController.UID;

        //Log.Info($"VictoryMissionController.VictoryMissionsInitUserTask url:{HttpServiceRoute.GetVictoryMissionInitUserTaskUrl} request:{request}");
        GatewayHttpService.PostWaiting(HttpServiceRoute.GetVictoryMissionInitUserTaskUrl, request.ToByteArray(), (status, resp) =>
        {
            if (status)
            {
                MultiWinningMissionsInitUserTaskResponse response = ProtobufUtil.Parser<MultiWinningMissionsInitUserTaskResponse>(resp, () => new MultiWinningMissionsInitUserTaskResponse());
                //Log.Info($"VictoryMissionController.VictoryMissionsInitUserTask url:{HttpServiceRoute.GetVictoryMissionInitUserTaskUrl} response:{response}");
                UpdateUserDataResetTask(response);
                suc?.Invoke();
            }
            else
            {
                var error = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"VictoryMissionController.VictoryMissionsInitUserTask url:{HttpServiceRoute.GetVictoryMissionInitUserTaskUrl} error:{error}");
                fail?.Invoke();
            }
        }, null);
    }

    /// <summary>
    /// 设置玩家活动状态
    /// </summary>
    /// <param name="suc"></param>
    /// <param name="fail"></param>
    public void VictoryMissionsSetUserActStatus(Action<MultiWinningMissionsSetUserActStatusResponse> suc = null, Action fail = null)
    {
        MultiWinningMissionsSetUserActStatusRequest request = new MultiWinningMissionsSetUserActStatusRequest();
        request.Uid = _userController.UID;

        //Log.Info($"VictoryMissionController.VictoryMissionsSetUserActStatus url:{HttpServiceRoute.GetVictoryMissionSetUserActStatusUrl} request:{request}");
        GatewayHttpService.PostWaiting(HttpServiceRoute.GetVictoryMissionSetUserActStatusUrl, request.ToByteArray(), (status, resp) =>
        {
            if (status)
            {
                MultiWinningMissionsSetUserActStatusResponse response = ProtobufUtil.Parser<MultiWinningMissionsSetUserActStatusResponse>(resp, () => new MultiWinningMissionsSetUserActStatusResponse());
                //Log.Info($"VictoryMissionController.VictoryMissionsSetUserActStatus url:{HttpServiceRoute.GetVictoryMissionSetUserActStatusUrl} response:{response}");
                SetTaskEndTime(response.TimeLimit);
                suc?.Invoke(response);
            }
            else
            {
                var error = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"VictoryMissionController.VictoryMissionsSetUserActStatus url:{HttpServiceRoute.GetVictoryMissionSetUserActStatusUrl} error:{error}");
                fail?.Invoke();
            }
        }, null);
    }

    /// <summary>
    /// 活动下线的时候请求连胜任务补发
    /// </summary>
    /// <param name="suc"></param>
    /// <param name="fail"></param>
    public void RequestGetVictoryRewardReissue(long aid, Action<MultiWinningMissionsCompensateRewardResponse> suc = null, Action fail = null)
    {
        MultiWinningMissionsCompensateRewardRequest request = new MultiWinningMissionsCompensateRewardRequest();
        request.Uid = _userController.UID;
        request.Aid = aid;

        Log.Info($"VictoryMissionController.RequestGetVictoryRewardReissue url:{HttpServiceRoute.MultiWinningMissionsCompensateRewardUrl} request:{request}");
        GatewayHttpService.PostWaiting(HttpServiceRoute.MultiWinningMissionsCompensateRewardUrl, request.ToByteArray(), (status, resp) =>
        {
            if (status)
            {
                MultiWinningMissionsCompensateRewardResponse response = ProtobufUtil.Parser<MultiWinningMissionsCompensateRewardResponse>(resp, () => new MultiWinningMissionsCompensateRewardResponse());
                Log.Info($"VictoryMissionController.RequestGetVictoryRewardReissue url:{HttpServiceRoute.MultiWinningMissionsCompensateRewardUrl} response:{response}");
                suc?.Invoke(response);
            }
            else
            {
                var error = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"VictoryMissionController.RequestGetVictoryRewardReissue url:{HttpServiceRoute.GetVictoryMissionRewardUrl} error:{error}");
                fail?.Invoke();
            }
        }, null);
    }

    /// <summary>
    /// 连胜任务补发
    /// </summary>
    /// <param name="complete"></param>
    public void VictoryRewardReissue(Action complete)
    {
        var activityController = ControllerManager.Get<ActivityController>();
        ReissueReward.Clear();
        if(activityController.IsOnline(actType))
        {
            if(UserData == null)
            {
                complete?.Invoke();
            }
            int stage = (int)UserData.ThisStage;
            GetVictoryMissionReward((int)UserData.ThisTaskIndex, (res) =>
            {
                ReissueReward.Add(res.Reward);
                GetVictoryMissionRateReward(stage, (res2) =>
                {
                    ReissueReward.Add(res2.Reward);
                    ShowReissueRewardView(ReissueReward, ActivityInfo.Theme, closeCallback: complete);
                    UpdateUserData();
                }, () =>
                {
                    if (ReissueReward.Count == 0)
                    {
                        complete?.Invoke();
                    }
                    else
                    {
                        ShowReissueRewardView(ReissueReward, ActivityInfo.Theme, closeCallback: complete);
                        UpdateUserData();
                    }
                });
            }, () =>
            {
                GetVictoryMissionRateReward(stage, (res2) =>
                {
                    ReissueReward.Add(res2.Reward);
                    ShowReissueRewardView(ReissueReward, ActivityInfo.Theme, closeCallback: complete);
                    UpdateUserData();
                }, () =>
                {
                    if (ReissueReward.Count != 0)
                    {
                        ShowReissueRewardView(ReissueReward, ActivityInfo.Theme, closeCallback: complete);
                        UpdateUserData();
                    }
                    else
                    {
                        complete?.Invoke();
                    }
                });
            });
        }
        else
        {
            activityController.RequestWaitGotRewardList((res) =>
            {
                Log.Info($"VictoryMissionController VictoryRewardReissue res: {res}");
                bool hasVictoryReward = false;
                foreach (var item in res)
                {
                    Log.Info($"VictoryMissionController VictoryRewardReissue item.ActType: {item.ActType} actType: {actType}");
                    if (item.ActType == actType)
                    {
                        Log.Info($"VictoryMissionController VictoryRewardReissue has reward item.Aid: {item.Aid}");
                        hasVictoryReward = true;
                        RequestGetVictoryRewardReissue(item.Aid, (res2) =>
                        {
                            ReissueReward.Add(res2.Reward);
                            ShowReissueRewardView(ReissueReward, ActivityInfo.Theme, closeCallback: complete);
                        }, () =>
                        {
                            complete?.Invoke();
                        });
                        break;
                    }
                }
                if (!hasVictoryReward)
                {
                    complete?.Invoke();
                }
            }, complete);
        }
    }

    private void UpdateUserData()
    {
        _activityController.RequestActUserDataByActType<MultiWinningMissionsUserData>(actType, (config) =>
        {
            Log.Info($"VictoryMissionController.TryInit userData:{config}");
            _userData = config;
        });
    }
}

public partial class VictoryMissionController : BaseActivityController
{
    NetworkController _networkController = ControllerManager.Get<NetworkController>();
    public VictoryMissionController()
    {
        _networkController.AddRecvCallback(WebsocketCommand.MultiWinningMissionsTaskFirstAddRate, OnTaskFirstProgressAdd);
        _networkController.AddRecvCallback(WebsocketCommand.MultiWinningMissionsTaskFinish, OnActTaskFinish);
    }

    public override void Dispose()
    {
        _networkController.RemoveRecvCallback(WebsocketCommand.MultiWinningMissionsTaskFirstAddRate, OnTaskFirstProgressAdd);
        _networkController.RemoveRecvCallback(WebsocketCommand.MultiWinningMissionsTaskFinish, OnActTaskFinish);
        base.Dispose();
    }

    public override void TryInit()
    {
        _activityController.RequestActConfigByActType<MultiWinningMissionsConfig>(actType, (config) =>
        {
            _config = config;
            Log.Info($"VictoryMissionController.TryInit config:{config}");
        });
        _activityController.RequestActUserDataByActType<MultiWinningMissionsUserData>(actType, (config) =>
        {
            Log.Info($"VictoryMissionController.TryInit userData:{config}");
            _userData = config;
        });
        BagReward.Clear();
    }

    public override void Refresh()
    {
        //base.Refresh();
        _activityController.RequestActConfigByActType<MultiWinningMissionsConfig>(actType, (config) =>
        {
            _config = config;
            Log.Info($"VictoryMissionController.Refresh config:{config}");
        });
        _activityController.RequestActUserDataByActType<MultiWinningMissionsUserData>(actType, (config) =>
        {
            Log.Info($"VictoryMissionController.Refresh userData:{config}");
            _userData = config;
        });
    }

    public void OpenView(bool isTask = false, Action cb = null)
    {
        if(!IsVaild())
        {
            cb?.Invoke();
            return;
        }
        UIManager.Instance.OpenActViewByType(UIType.VictoryMissionView, ActType.VictoryMission, isTask);
    }

    /// <summary>
    /// 显示补发奖励
    /// </summary>
    /// <param name="prizeRewardArray"></param>
    /// <param name="theme"></param>
    /// <param name="closeCallback"></param>
    /// <param name="isWithLight"></param>
    /// <param name="isShowReward"></param>
    /// <param name="isShowBox"></param>
    /// <param name="boxType"></param>
    public void ShowReissueRewardView(RepeatedField<PrizeReward> prizeRewardArray, string theme, Action closeCallback = null, bool isWithLight = false, bool isShowReward = true, bool isShowBox = false, BoxType boxType = BoxType.None)
    {
        //CurShowRewardType = TwistPuzzleRewardWindowType.Reissue;
        if (prizeRewardArray != null && prizeRewardArray.Count > 0)
        {
            UIManager.Instance.OpenActViewByTheme(UIType.VicotryMissionRewardView, theme, new UIDisplayPropViewData(prizeRewardArray, closeCallback, isWithLight, isShowBox, boxType, UIDisplayPropViewData.ShowType.Manual));
        }
        else
        {
            closeCallback?.Invoke();
            ControllerManager.Get<TaskController>().Done(WaitingTaskType.VictoryMissionReissueTask);
        }
    }

    private void UpdateUserDataResetTask(MultiWinningMissionsInitUserTaskResponse data)
    {
        MultiWinningMissionsUserData tData = _userData;
        tData.CurRate = data.Data.CurRate;
        tData.CurFinishStage.Clear();
        tData.CurStage.Clear();
        // tData.CurStage.AddRange(data.Data.CurStage);
        tData.FailedTimes = data.Data.FailedTimes;
        tData.RcStage.Clear();
        tData.RcStage.AddRange(data.Data.RcStage);
        tData.TaskEndTime = data.Data.TaskEndTime;
        tData.ThisStage = data.Data.ThisStage;
        tData.ThisTaskIndex = data.Data.ThisTaskIndex;
        _userData = tData;
        Log.Info($"VictoryMissionController.ResetTask userData:{_userData}");
    }

    private void UpdateUserDataTaskReward(ReceiveMultiWinningMissionsStageRewardResponse data)
    {
        MultiWinningMissionsUserData tData = _userData;
        tData.CurRate = data.Data.CurRate;
        tData.CurFinishStage.Clear();
        tData.CurFinishStage.AddRange(data.Data.CurFinishStage);
        tData.RcStage.Clear();
        tData.RcStage.AddRange(data.Data.RcStage);
        Log.Info($"VictoryMissionController.UpdateUserDataTaskReward data.Data.ThisTaskIndex: {data.Data.ThisTaskIndex}");
        tData.ThisTaskIndex = data.Data.ThisTaskIndex;
        tData.ActStatus = data.Data.ActStatus;
        tData.ThisStage = data.Data.ThisStage;
        tData.CurRound = data.Data.CurRound;
        tData.RcRate.Clear();
        tData.RcRate.AddRange(data.Data.RcRate);
        tData.TaskEndTime = 0;
        tData.FailedTimes = data.Data.FailedTimes;
        _userData = tData;

        RepeatedField<PrizeReward> _reward = data.Reward;
        BagReward.Add(_reward);
        Log.Info($"VictoryMissionController.UpdateUserDataTaskReward _reward:{_reward}");
        Log.Info($"VictoryMissionController.UpdateUserDataTaskReward userData:{_userData}");
    }

    private void UpdateUserDataStageRrward(ReceiveMultiWinningMissionsRateRewardResponse data)
    {
        MultiWinningMissionsUserData tData = _userData;
        tData.RcRate.Clear();
        tData.RcRate.AddRange(data.RcRate);
        tData.ThisStage = data.ThisStage;
        tData.CurStage.Clear();
        tData.CurFinishStage.Clear();
        tData.FailedTimes = 0;
        tData.ThisTaskIndex = 1;

        RepeatedField<PrizeReward> _reward = data.Reward;
        BagReward.Add(_reward);
    }

    public bool IsVaild()
    {
        return ActivityInfo != null && Config != null && UserData != null;

    }

    public bool IsCreatBtn()
    {
        if(UserData == null)
        {
            return false;
        }
        Log.Info($"VictoryMissionController IsCreatBtn UserData.RcRate.Count: {UserData.RcRate.Count} UserData.ThisStage: {UserData.ThisStage}");
        return UserData.RcRate.Count != UserData.ThisStage;
    }

    protected void OnTaskFirstProgressAdd(int cmd, byte[] data, int requestId)
    {
        Log.Info($"VictoryMissionController msg OnTaskFirstProgressAdd IsVaild:{IsVaild()} requestId:{requestId} data:{data}");
        if (IsVaild())
        {
            TaskController _taskController = ControllerManager.Get<TaskController>();
            Func<WaitingTask, bool> condition = new Func<WaitingTask, bool>(task =>
            {
                return task.TaskType == WaitingTaskType.VictoryMissionTask;
            });
            Log.Info($"VictoryMissionController msg OnTaskFirstProgressAdd _taskController.FindTask(WaitingTaskType.VictoryMissionTask, condition):{_taskController.FindTask(WaitingTaskType.VictoryMissionTask, condition) == null}");
            if (_taskController.FindTask(WaitingTaskType.VictoryMissionTask, condition) == null)
            {
                _taskController.AddTask(new VictoryMissionTask());
            }
            //_redDotController.SetRedDotStatus(RedDotKey.Activity_VictoryMissionBtn, 1);
            return;
        }
    }

    protected void OnActTaskFinish(int cmd, byte[] data, int requestId)
    {
        Log.Info($"VictoryMissionController msg OnActTaskFinish IsVaild:{IsVaild()} ");
        if (IsVaild())
        {
            // ActTaskFinishMsg  message = ProtobufUtil.Parser<ActTaskFinishMsg >(data, () => new ActTaskFinishMsg ());
            // Log.Info($"VictoryMissionController msg OnTaskFirstProgressAdd message:{message}");
            TaskController _taskController = ControllerManager.Get<TaskController>();
            Func<WaitingTask, bool> condition = new Func<WaitingTask, bool>(task =>
            {
                return task.TaskType == WaitingTaskType.VictoryMissionTask;
            });
            Log.Info($"VictoryMissionController msg OnActTaskFinish _taskController.FindTask(WaitingTaskType.VictoryMissionTask, condition):{_taskController.FindTask(WaitingTaskType.VictoryMissionTask, condition) == null}");
            if(_taskController.FindTask(WaitingTaskType.VictoryMissionTask, condition) == null)
            {
                _taskController.AddTask(new VictoryMissionTask());
            }
            _redDotController.SetRedDotStatus(RedDotKey.Activity_VictoryMissionBtn, 1);
            return;
        }
    }

    private void SetTaskEndTime(long time)
    {
        UserData.TaskEndTime = time;
        Log.Info($"VictoryMissionController.SetTaskEndTime time:{time}");
    }

    public void UpdateUserData(Action suc = null, Action fail = null)
    {
        _activityController.RequestActUserDataByActType<MultiWinningMissionsUserData>(actType, (config) =>
        {
            Log.Info($"VictoryMissionController.TryInit userData:{config}");
            _userData = config;
            suc?.Invoke();
        }, () =>
        {
            fail?.Invoke();
        });
    }

    /// <summary>
    /// 任务点击跳转事件
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    public Action TaskClickAction(long taskId)
    {
        if(TaskClickDic.TryGetValue(taskId, out Action action))
        {
            return action;
        }
        return null;
    }

    public Dictionary<long, Action> TaskClickDic = new Dictionary<long, Action>
    {
        //偷次数
        { 2, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //占领次数
        { 3, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //获取卡片数
        { 4, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Album);
        }},
        //登录天数 
        { 5, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Operate);
        }},
        //投掷骰子次数
        { 7, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //锦标赛排名前3
        { 8, () =>
        {
            //EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //锦标赛第1名
        { 9, () =>
        {
            //EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //获取彩虹卡次数
        { 10, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Album);
        }},
        //使用小丑卡次数
        { 11, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Album);
        }},
        //邀请好友
        { 12, () =>
        {
            InviteController _controller = ControllerManager.Get<InviteController>();
            _controller.RequestGetUserInviteRewardData();
        }},
        //收集金卡数
        { 14, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Album);
        }},
        //帮助好友刮卡次数
        { 16, () =>
        {
            Waiting.NotifyShow();
            if(ControllerManager.Get<FriendLotteryController>().AllFriendLotteryList.Count > 0)
            {
                FriendLotteryController _lotteryController = ControllerManager.Get<FriendLotteryController>();
                EventCenter.Instance.Event.TriggerEvent(EventType.FriendLotteryGuid.ClickScratchBtn);
                _lotteryController.RequestGetFriendLotteryList(cb: (resp) =>
                {
                    Waiting.NotifyDispose();
                    if (resp.LotteryList.Count > 0)
                    {
                        UIManager.Instance.Open(UIType.FriendLotteryMainView);
                    }
                }, () =>{ Waiting.NotifyDispose(); });
            }
            else
            {
                Waiting.NotifyDispose();
                EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Operate);
            }
        }},
        //亲密度增加值
        { 17, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Friend);
        }},
        //付费次数
        { 19, () =>
        {
            ControllerManager.Get<ShopController>().OpenShopView();
        }},
        //幸运冲刺代币使用次数
        { 20, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Operate);
        }},
        //锦标赛排名前5
        { 21, () =>
        {
            //EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //锦标赛排名前10
        { 22, () =>
        {
            //EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //锦标赛排名前20
        { 23, () =>
        {
            //EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //锦标赛排名前30
        { 24, () =>
        {
            //EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //获得盾牌数量
        { 25, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //分享
        { 26, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
        //疯狂游客次数
        { 2007, () =>
        {
            EventCenter.Instance.Event.TriggerEvent<BtnState>(EventType.MainView.ChangeView, BtnState.Dice);
        }},
    };
}

#endif