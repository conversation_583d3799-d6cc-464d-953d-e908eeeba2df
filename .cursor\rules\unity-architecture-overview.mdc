---
description:
globs:
alwaysApply: false
---
# Unity 项目架构与开发规范

## 目录结构
- 主要业务代码位于 [Assets/Scripts/](mdc:Assets/Scripts) 下，分为 GameFrame（基础框架）、HotUpdate（热更业务）、GameFrame/TKComponents（核心组件）等。
- 控制器、模型、管理器等核心类在 [Assets/Scripts/GameFrame/TKComponents/MVC/](mdc:Assets/Scripts/GameFrame/TKComponents/MVC) 及其子目录。
- 事件系统在 [Assets/Scripts/GameFrame/TKComponents/EventCenter/](mdc:Assets/Scripts/GameFrame/TKComponents/EventCenter)。
- 日志相关在 [Assets/Scripts/GameFrame/TKComponents/Diagnostics/](mdc:Assets/Scripts/GameFrame/TKComponents/Diagnostics)。
- 工具类（如本地化、配置）在 [Assets/Scripts/GameFrame/TKComponents/Utility/](mdc:Assets/Scripts/GameFrame/TKComponents/Utility)。

## 核心约定
- 错误处理：文件/网络操作需用 try-catch。
- 资源清理：事件、资源等需在 OnDestroy 做清理。
- 遵循 C# 命名规范和 Unity 最佳实践。

---

如需查找具体实现，可参考上述各目录和文件。
