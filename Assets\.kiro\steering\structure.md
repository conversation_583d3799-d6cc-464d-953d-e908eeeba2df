# Project Structure

## Root Directory Organization

```
├── Art/                    # Game assets (textures, prefabs, UI, shaders)
├── Scripts/               # All C# source code
│   ├── GameFrame/         # Core framework and base classes
│   └── HotUpdate/         # Hot-updatable game logic
├── Editor/                # Unity editor tools and build scripts
├── Extensions/            # Third-party plugins and extensions
├── Resources/             # Unity Resources folder
├── StreamingAssets/       # Build-time assets and configuration
├── Plugins/               # Native plugins and DLLs
├── Scene/                 # Unity scene files
└── TestScene/             # Development and testing scenes
```

## Scripts Architecture

### GameFrame (Core Framework)
- `TKComponents/` - Base components and utilities
  - `Diagnostics/` - Logging system (Log.cs, Diagnostics.cs)
  - `SdkWrapper/` - Platform SDK integrations
- Foundation classes that don't change frequently

### HotUpdate (Game Logic)
- `Scripts/Game/` - Core game systems
  - `Config/` - Configuration management
  - `Module/` - Game feature modules
- `Scripts/Components/` - Reusable game components
  - `Wrapper/` - Service wrappers (AdController, etc.)
  - `Network/` - HTTP services and API clients
  - `Utils/` - Utility classes
  - `UISystem/` - UI framework and base classes
- `Scripts/DiceGame/` - Dice game specific logic
- All hot-updatable game content

## Key Conventions

### Naming Patterns
- **Controllers**: `[Feature]Controller` (e.g., `AdController`, `WrapperController`)
- **Models**: `[Feature]Model` (e.g., `ConfigModel`)
- **Views**: `UI[Feature]View` (e.g., `UIMainView`, `UIArchitectureMasterView`)
- **Utilities**: `[Feature]Util` (e.g., `TrackUtil`, `SaveUtils`)

### File Organization
- Group related functionality in dedicated folders
- Separate hot-updatable code from core framework
- Keep platform-specific code in appropriate directories
- Use consistent folder structure across modules

### Configuration Management
- JSON-based configuration system
- Multi-language support with separate config files
- Version-controlled configuration updates
- Platform-specific configurations (iOS/Android)

## Asset Organization

### Art Assets
- `Art/UI/` - User interface assets
- `Art/Prefab/` - Game object prefabs
- `Art/Shader/` - Custom shaders and materials
- `Art/_DiceGame/` - Dice game specific assets
- `Art/_SlotGame/` - Slot game specific assets

### Build Assets
- `StreamingAssets/` - Runtime configuration and version info
- `Resources/` - Unity Resources for immediate loading
- Asset bundles managed through YooAsset system

## Development Guidelines

### Code Structure
- Inherit from `TKController` for service classes
- Inherit from `TKModel` for data models
- Use `ControllerManager.Get<T>()` for service access
- Implement proper logging with `Log.Info()`, `Log.Warning()`, `Log.Error()`

### Hot Update Separation
- Core framework in `Scripts/GameFrame/` (not hot-updatable)
- Game logic in `Scripts/HotUpdate/` (hot-updatable)
- Clear separation ensures stable foundation with updatable content