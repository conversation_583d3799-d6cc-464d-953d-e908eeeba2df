# 上报失败导致收集活动数据对不上的问题分析

## 问题根源

### 当前流程中的问题

1. **RollOnAnim() 中的处理顺序**：
```csharp
private void RollOnAnim()
{
    // 1. 获取投掷结果
    DiceFaceArrayToPlay = GetCurDiceRusult(); // 这里已经将结果移到WaitingSubmitSequence
    
    // 2. 处理奖励类型（包括收集数据）
    HandleRewardType(DiceFaceArrayToPlay); // 创建ActScoreData
    
    // 3. 触发网络投掷事件
    EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);
    
    // 4. 上报投掷结果
    SubmitRollResult(); // 可能失败
}
```

2. **收集任务的触发时机**：
```csharp
// 在 DiceMainRewardLayerBehaviour.cs 中
if (hasSlotCollect)
{
    _diceController.AddCollectTask(result.Result, result.ActData); // 立即添加收集任务
    await UniTask.Delay(50);
}
```

3. **收集任务的执行**：
```csharp
// 在 SlotTaskCollect.cs 中
public void Execute()
{
    // 直接触发收集事件，更新本地UI
    EventCenter.Instance.Event.TriggerEvent<ActScoreData>(EventType.SLOT.ResultCollect, scoreData);
}
```

4. **上报失败的处理**：
```csharp
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    if (code != 0) // 上报失败
    {
        // 只是清空队列，但收集数据已经被本地处理了
        WaitingSubmitSequence.Clear();
        RequestBeforeResults((list) => {
            DicePlayingSequence = list; // 重新获取服务器数据
        }, null);
    }
}
```

## 问题的具体表现

1. **本地收集进度超前**：
   - 用户看到收集进度增加了
   - 但服务器实际没有记录这次收集
   - 下次同步时会发现数据不一致

2. **重复计算收集积分**：
   - 如果上报失败后重新播放相同结果
   - 可能会再次触发收集事件
   - 导致本地积分重复增加

3. **服务器数据丢失**：
   - 上报失败意味着服务器没有收到收集数据
   - 但本地已经消费了这个结果
   - 造成永久性的数据不一致

## 解决方案

### 方案1：延迟收集数据处理（推荐）

将收集数据的处理延迟到上报成功之后：

1. **修改 RollOnAnim() 流程**：
   - 只处理基础奖励（金币、体力等）
   - 收集数据处理移到上报成功回调中

2. **在上报成功回调中处理收集**：
   - 确保服务器已确认收到数据
   - 再触发本地收集事件和UI更新

### 方案2：增加收集数据回滚机制

在上报失败时回滚已处理的收集数据：

1. **记录收集操作**：
   - 在处理收集数据前记录操作
   - 包括增加的积分、阶段变化等

2. **上报失败时回滚**：
   - 撤销本地收集进度更新
   - 恢复到处理前的状态

### 方案3：服务器数据优先

完全依赖服务器数据，本地不做预处理：

1. **移除本地收集计算**：
   - 不在本地计算收集积分
   - 等待服务器返回最终数据

2. **服务器返回完整数据**：
   - 包括更新后的收集进度
   - 本地直接应用服务器数据

## 推荐的具体修改方案

### 修改1：调整 RollOnAnim() 流程

```csharp
private void RollOnAnim()
{
    // 记录上一次金币同步的事件戳
    LastSyncCoinsTs = ControllerManager.Get<UserController>().SyncCoinsTs;

    // 暂停投掷状态机
    PauseRollDice();
    // 消耗体力
    _userController.Spins -= _lastSlotBet;
    // 记录体力消耗
    TrackUtil.Consumespins(_lastSlotBet);
    // 获取当前投掷结果
    DiceFaceArrayToPlay = GetCurDiceRusult();

    // 只处理基础奖励，不处理收集数据
    HandleBasicRewardType(DiceFaceArrayToPlay);

    // 触发网络投掷事件
    EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);

    // 上报投掷结果，成功后处理收集数据
    SubmitRollResult(() => {
        // 上报成功后处理收集数据
        HandleCollectRewardType(DiceFaceArrayToPlay);
    });
}
```

### 修改2：分离基础奖励和收集奖励处理

```csharp
// 新增：只处理基础奖励
private void HandleBasicRewardType(long[] diceFaces)
{
    DiceRollResult.Clear();

    // 将diceFaces中的每个元素添加到Result中
    foreach (var slotId in diceFaces)
    {
        DiceRollResult.Result.Add(slotId);
    }

    // 检查是否所有骰子都是相同面值
    DiceRollResult.IsSameResult = AllDiceType(diceFaces, diceFaces[0]);

    // 只处理基础奖励：金币、体力、盾牌等
    // 不处理收集数据
    if (AllDiceType(diceFaces, (int)SlotId.SpinsIcon))
    {
        DiceRollResult.RewardType = DiceRewardType.Spin;
        DiceRollResult.Spins = SlotBet * 10;
    }
    // ... 其他基础奖励处理
}

// 新增：处理收集奖励
private void HandleCollectRewardType(long[] diceFaces)
{
    // 处理收集物奖励
    long collectNum = GetCollectNum(diceFaces, DiceRollResult.RewardType);
    if (collectNum > 0)
    {
        DiceRollResult.IsAllCollect = AllDiceType(diceFaces, (int)SlotId.CollectIcon);

        // 创建活动分数数据
        ActScoreData actScoreData = new ActScoreData();
        actScoreData.Aid = ControllerManager.Get<CollectController>().GetCurrInfo().Aid;
        actScoreData.ActType = ActType.Collect;
        actScoreData.IncrScore = collectNum * SlotBet;

        DiceRollResult.ActData = new RepeatedField<ActScoreData>();
        DiceRollResult.ActData.Add(actScoreData);
        DiceRollResult.CollectNum = collectNum * SlotBet;

        // 现在可以安全地触发收集任务
        TriggerCollectTasks();
    }
}
```

### 修改3：增加收集任务触发方法

```csharp
// 新增：安全地触发收集任务
private void TriggerCollectTasks()
{
    CollectController cc = ControllerManager.Get<CollectController>();
    var rollResult = DiceRollResult.Result;
    bool hasSlotCollect = cc.IsSlotResuldTriggerCollect(rollResult[0], rollResult[1], rollResult[2]);

    if (hasSlotCollect && _activityContoller.IsOnline(ActType.Collect))
    {
        AddCollectTask(DiceRollResult.Result, DiceRollResult.ActData);
    }
}
```

### 修改4：改进上报失败处理

```csharp
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    Timer.ClearTimer(_timesetReconnectOnUp);
    _timesetReconnectOnUp = 0;
    IsUping = false;

    if (code == 0) // 上报成功
    {
        var response = ProtobufUtil.Parser<SubSlotResultResponse>(resp,
            () => new SubSlotResultResponse());

        // 移除已成功上报的结果
        for (int i = 0; i < hasPreUpCount; i++)
        {
            WaitingSubmitSequence.RemoveAt(0);
        }

        // 执行上报完成回调（包括收集数据处理）
        UpAction?.Invoke();
    }
    else // 上报失败
    {
        Log.Info($"批量投掷相关 上报失败，需要重新同步数据");

        // 清空待上报队列
        WaitingSubmitSequence.Clear();

        // 重新获取服务器数据，确保数据一致性
        RequestBeforeResults((list) => {
            DicePlayingSequence = list;

            // 如果有待播放的结果，重新播放
            if (DicePlayingSequence.Count > 0)
            {
                RollOnAnim();
            }
        }, null);
    }
}
```

## 额外的安全措施

### 1. 添加收集数据状态标记

```csharp
public partial class DiceController
{
    // 新增：标记收集数据是否已处理
    private bool _isCollectDataProcessed = false;

    // 新增：待处理的收集数据
    private long[] _pendingCollectData = null;
}
```

### 2. 防重复处理机制

```csharp
private void HandleCollectRewardType(long[] diceFaces)
{
    // 防止重复处理
    if (_isCollectDataProcessed)
    {
        Log.Info("收集数据已处理，跳过重复处理");
        return;
    }

    long collectNum = GetCollectNum(diceFaces, DiceRollResult.RewardType);
    if (collectNum > 0)
    {
        _isCollectDataProcessed = true;
        // ... 处理收集数据
    }
}
```

### 3. 重置状态标记

```csharp
private long[] GetCurDiceRusult()
{
    if (DicePlayingSequence == null || DicePlayingSequence.Count == 0)
    {
        return null;
    }

    var first = DicePlayingSequence[0];
    DicePlayingSequence.RemoveAt(0);
    WaitingSubmitSequence.Add(first);

    // 重置收集数据处理状态
    _isCollectDataProcessed = false;
    _pendingCollectData = first;

    return first;
}
```

## 测试验证方案

### 1. 模拟上报失败场景

```csharp
// 在测试环境中强制上报失败
public void ForceSubmitFailure()
{
    // 模拟网络问题导致上报失败
    IsUping = false;
    OnReceiveSubmit(-1, null, 0); // 模拟失败响应
}
```

### 2. 数据一致性检查

```csharp
public bool VerifyCollectDataConsistency()
{
    // 检查本地收集数据与服务器数据是否一致
    var localData = ControllerManager.Get<CollectController>().GetCurrUserData();

    // 请求服务器最新数据进行对比
    RequestBeforeResults((serverList) => {
        // 对比逻辑
        bool isConsistent = CompareCollectData(localData, serverList);
        Log.Info($"收集数据一致性检查结果: {isConsistent}");
        return isConsistent;
    });

    return true;
}
```

## 监控和日志

### 1. 增加详细日志

```csharp
private void LogCollectDataOperation(string operation, long[] diceData, ActScoreData scoreData)
{
    Log.Info($"[收集数据操作] {operation} - 骰子结果: [{string.Join(",", diceData)}] - 积分数据: {scoreData}");
}
```

### 2. 数据状态监控

```csharp
public void MonitorCollectDataState()
{
    Log.Info($"[收集数据状态] 待播放队列: {DicePlayingSequence.Count}, 待上报队列: {WaitingSubmitSequence.Count}, 是否已处理: {_isCollectDataProcessed}");
}
```

这些修改将确保收集活动数据的一致性，避免上报失败导致的数据不同步问题。

## 改进的上报机制方案（基于用户需求）

### 核心思路
1. **保持动画流畅性**：收集数据立即处理，UI立即更新
2. **确保上报完整性**：每次上报都将本地所有待上报的队列全部上报
3. **依赖操作等待**：收集领奖、偷打、事件牌等操作等待上报完成

### 方案1：增强上报队列管理

#### 1.1 修改上报逻辑 - 确保队列完整上报

```csharp
/// <summary>
/// 上报投掷结果 - 改进版本
/// 每次上报都将本地所有待上报的队列全部上报
/// </summary>
/// <param name="cb">上报完成后的回调函数</param>
public void SubmitRollResult(Action cb = null)
{
    // 检查是否有待上报的结果
    if ((WaitingSubmitSequence == null || WaitingSubmitSequence.Count == 0))
    {
        Log.Info($"批量投掷相关 没有要上报的，将直接执行回调");
        cb?.Invoke();
        return;
    }

    // 如果正在上报中，将回调添加到队列中等待上报完成
    if (IsUping)
    {
        Log.Info($"批量投掷相关 正在上报中，将在此次上报结束后继续");
        Action NewCb = UpAction;
        UpAction = () =>
        {
            NewCb?.Invoke();
            SubmitRollResult(cb); // 递归调用确保所有数据都上报
        };
        return;
    }

    // 设置上报完成后的回调
    UpAction = cb;
    var user = ControllerManager.Get<UserController>();

    // 构建上报请求消息 - 上报所有待提交的结果
    SubSlotResultRequest msg = new SubSlotResultRequest() { Uid = user.UID };

    // 将本地所有待上报结果转换为服务器格式
    var allResults = ReturnToMessegeData(WaitingSubmitSequence);
    for (int i = 0; i < allResults.Count; i++)
    {
        msg.Results.Add(allResults[i]);
    }

    // 发送上报请求到服务器
    ControllerManager.Get<NetworkController>().SendMsg(WebsocketCommand.SubSlotList,
        ByteString.CopyFrom(msg.ToByteArray()));

    // 记录本次上报的结果数量（所有待上报的）
    hasPreUpCount = allResults.Count;
    IsUping = true;

    Log.Info($"批量投掷相关 准备上报所有待提交结果，数量: {hasPreUpCount}");

    // 设置超时处理
    SetupSubmitTimeout();
}
```

#### 1.2 分离超时处理逻辑

```csharp
/// <summary>
/// 设置上报超时处理
/// </summary>
private void SetupSubmitTimeout()
{
    if (_timesetReconnectOnUp == 0)
    {
        _timesetReconnectOnUp = Timer.SetTimeout(6f, () =>
        {
            Log.Info($"批量投掷相关 超过6秒上报未反应，开始重新同步");
            PreventWebEorror();

            // 获取服务器端的数据进行对比
            RequestBeforeResults((NetList) =>
            {
                // 对比本地和服务器数据
                bool isSameList = IsSameUpList(NetList, WaitingSubmitSequence);
                Log.Info($"批量投掷相关 本地待上报队列和服务器一致吗: {isSameList}");

                IsUping = false;
                if (!isSameList)
                {
                    // 数据不一致，重新上报所有待提交数据
                    Log.Info("数据不一致，重新上报所有待提交数据");
                    SubmitRollResult();
                }
                else
                {
                    // 数据一致，说明是网络问题，清空本地队列
                    Log.Info("数据一致，清空本地待上报队列");
                    WaitingSubmitSequence.Clear();
                    UpAction?.Invoke(); // 执行回调
                }
            });
        });
    }
}
```

#### 1.3 改进上报响应处理

```csharp
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    // 清理超时计时器
    if (_timesetReconnectOnUp != 0)
    {
        Timer.ClearTimer(_timesetReconnectOnUp);
        _timesetReconnectOnUp = 0;
    }

    IsUping = false;

    if (code == 0) // 上报成功
    {
        var response = ProtobufUtil.Parser<SubSlotResultResponse>(resp,
            () => new SubSlotResultResponse());
        Log.Info($"批量投掷相关 上报成功，清空所有已上报数据: {hasPreUpCount}");

        // 清空所有已成功上报的结果
        WaitingSubmitSequence.Clear();

        // 执行上报完成后的回调
        UpAction?.Invoke();
    }
    else // 上报失败
    {
        Log.Info($"批量投掷相关 上报失败，错误码: {code}");

        // 上报失败，重新获取服务器数据进行同步
        RequestBeforeResults((serverList) =>
        {
            // 用服务器数据覆盖本地待播放队列
            DicePlayingSequence = serverList;

            // 清空待上报队列，因为服务器没有收到
            WaitingSubmitSequence.Clear();

            Log.Info($"批量投掷相关 上报失败后重新同步，服务器队列长度: {serverList.Count}");

            // 如果还有待播放的数据，重新开始播放流程
            if (DicePlayingSequence.Count > 0)
            {
                // 不立即播放，等待当前流程完成
                Timer.SetTimeout(0.1f, () =>
                {
                    if (!IsRolling) // 确保当前没有在滚动
                    {
                        RollOnAnim();
                    }
                });
            }
        }, null);
    }
}
```

### 方案2：依赖操作等待机制

#### 2.1 添加上报完成等待接口

```csharp
public partial class DiceController
{
    /// <summary>
    /// 等待上报完成的回调队列
    /// </summary>
    private Queue<Action> _waitingForSubmitCallbacks = new Queue<Action>();

    /// <summary>
    /// 等待上报完成后执行操作
    /// </summary>
    /// <param name="callback">上报完成后要执行的回调</param>
    public void WaitForSubmitComplete(Action callback)
    {
        if (!IsUping && WaitingSubmitSequence.Count == 0)
        {
            // 没有待上报数据，直接执行
            callback?.Invoke();
        }
        else
        {
            // 有待上报数据，加入等待队列
            _waitingForSubmitCallbacks.Enqueue(callback);
            Log.Info($"添加等待上报完成的回调，当前队列长度: {_waitingForSubmitCallbacks.Count}");
        }
    }

    /// <summary>
    /// 执行所有等待上报完成的回调
    /// </summary>
    private void ExecuteWaitingCallbacks()
    {
        Log.Info($"执行等待上报完成的回调，队列长度: {_waitingForSubmitCallbacks.Count}");
        while (_waitingForSubmitCallbacks.Count > 0)
        {
            var callback = _waitingForSubmitCallbacks.Dequeue();
            try
            {
                callback?.Invoke();
            }
            catch (Exception e)
            {
                Log.Error($"执行等待回调时出错: {e}");
            }
        }
    }
}
```

#### 2.2 修改上报成功处理

```csharp
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    // ... 原有逻辑 ...

    if (code == 0) // 上报成功
    {
        // ... 原有处理 ...

        // 执行上报完成后的回调
        UpAction?.Invoke();

        // 执行所有等待上报完成的回调
        ExecuteWaitingCallbacks();
    }
    else // 上报失败
    {
        // ... 原有处理 ...

        // 上报失败时也要执行等待回调，避免死锁
        ExecuteWaitingCallbacks();
    }
}
```

#### 2.3 修改收集领奖请求

```csharp
// 在 CollectController.cs 中修改
public void RequestCollectStageRewardConfig(Action<RcCollectStageRewardResponse> cb = null, Action failCb = null, bool onlyRequest = false)
{
    // 等待上报完成后再请求领奖
    ControllerManager.Get<DiceController>().WaitForSubmitComplete(() =>
    {
        UserController u = ControllerManager.Get<UserController>();

        RcCollectStageRewardRequset request = new RcCollectStageRewardRequset();
        request.Uid = u.UID;
        request.ActType = ActType.Collect;
        request.Aid = activityController.GetAidByActtype(ActType.Collect);

        Log.Info($"[CollectController] 上报完成后请求收集领奖 RequestCollectStageRewardConfig url:{HttpServiceRoute.RcCollectStageRewardRequset} request:{request}");

        isRequestReward = true;
        GatewayHttpService.Post(HttpServiceRoute.RcCollectStageRewardRequset, request.ToByteArray(),
            (bool isSuccess, byte[] resp) =>
            {
                // ... 原有处理逻辑 ...
            }, () =>
            {
                // ... 原有错误处理 ...
            }, ERequestType.WaitingRetry);
    });
}
```

#### 2.4 修改攻击偷袭请求

```csharp
// 在 AttackController.cs 中修改
public void RequestGetAttacker(Action<GetStealOrAttackResponse> successCallback = null, Action failCallback = null)
{
    // 等待上报完成后再请求攻击
    ControllerManager.Get<DiceController>().WaitForSubmitComplete(() =>
    {
        UserController u = ControllerManager.Get<UserController>();
        GetStealOrAttackRequest request = new GetStealOrAttackRequest();
        request.Uid = u.UID;
        request.OptType = OptType.Attack;

        Log.Info($"[AttackController] 上报完成后请求攻击 Request url:{HttpServiceRoute.GetStealOrAttackUrl} : {request}");

        GatewayHttpService.Post(HttpServiceRoute.GetStealOrAttackUrl, request.ToByteArray(),
            (Action<bool, byte[]>)((status, resp) =>
            {
                // ... 原有处理逻辑 ...
            }), () =>
            {
                // ... 原有错误处理 ...
            }, ERequestType.WaitingRetry);
    });
}

// 在 StealController.cs 中类似修改
public void RequestGetStealer(Action<GetStealOrAttackResponse> successCallback = null, Action failCallback = null)
{
    // 等待上报完成后再请求偷袭
    ControllerManager.Get<DiceController>().WaitForSubmitComplete(() =>
    {
        // ... 原有请求逻辑 ...
    });
}
```

#### 2.5 修改事件牌请求

```csharp
// 在 EventCardController.cs 中修改
public void RequestGetEventResult(Action<EventCardDataType> successCallback = null)
{
    // 等待上报完成后再请求事件结果
    ControllerManager.Get<DiceController>().WaitForSubmitComplete(() =>
    {
        UserController u = ControllerManager.Get<UserController>();
        GetEventResultRequest getEventResultRequest = new GetEventResultRequest();
        getEventResultRequest.Uid = u.UID;

        Log.Info($"[EventCardController] 上报完成后请求事件结果 RequestGetEventResult url:{HttpServiceRoute.GetEventResult} request:{getEventResultRequest}");

        GatewayHttpService.PostWaiting(HttpServiceRoute.GetEventResult, getEventResultRequest.ToByteArray(),
            (Action<bool, byte[]>)(async (status, resp) =>
            {
                // ... 原有处理逻辑 ...
            }), () =>
            {
                // ... 原有错误处理 ...
            });
    });
}
```

### 方案3：增强数据一致性保障

#### 3.1 添加数据版本控制

```csharp
public partial class DiceController
{
    /// <summary>
    /// 数据版本号，用于检测数据一致性
    /// </summary>
    private long _dataVersion = 0;

    /// <summary>
    /// 待上报数据的版本映射
    /// </summary>
    private Dictionary<long[], long> _submitDataVersions = new Dictionary<long[], long>();

    /// <summary>
    /// 获取当前投掷结果（带版本控制）
    /// </summary>
    private long[] GetCurDiceRusultWithVersion()
    {
        if (DicePlayingSequence == null || DicePlayingSequence.Count == 0)
        {
            return null;
        }

        var first = DicePlayingSequence[0];
        DicePlayingSequence.RemoveAt(0);
        WaitingSubmitSequence.Add(first);

        // 为这个数据分配版本号
        _dataVersion++;
        _submitDataVersions[first] = _dataVersion;

        Log.Info($"获取投掷结果，版本号: {_dataVersion}, 结果: [{string.Join(",", first)}]");
        return first;
    }

    /// <summary>
    /// 清理已上报数据的版本信息
    /// </summary>
    private void ClearSubmittedDataVersions()
    {
        var keysToRemove = new List<long[]>();
        foreach (var kvp in _submitDataVersions)
        {
            if (!WaitingSubmitSequence.Contains(kvp.Key))
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        foreach (var key in keysToRemove)
        {
            _submitDataVersions.Remove(key);
        }
    }
}
```

#### 3.2 添加重复处理检测

```csharp
public partial class DiceController
{
    /// <summary>
    /// 已处理的收集数据版本
    /// </summary>
    private HashSet<long> _processedCollectVersions = new HashSet<long>();

    /// <summary>
    /// 处理收集奖励（防重复）
    /// </summary>
    private void HandleCollectRewardTypeWithCheck(long[] diceFaces)
    {
        // 获取这个数据的版本号
        if (!_submitDataVersions.TryGetValue(diceFaces, out long version))
        {
            Log.Warning("无法找到投掷数据的版本号，跳过收集处理");
            return;
        }

        // 检查是否已经处理过
        if (_processedCollectVersions.Contains(version))
        {
            Log.Info($"收集数据版本 {version} 已处理过，跳过重复处理");
            return;
        }

        // 标记为已处理
        _processedCollectVersions.Add(version);

        // 处理收集物奖励
        long collectNum = GetCollectNum(diceFaces, DiceRollResult.RewardType);
        if (collectNum > 0)
        {
            Log.Info($"处理收集数据版本 {version}，收集数量: {collectNum}");

            DiceRollResult.IsAllCollect = AllDiceType(diceFaces, (int)SlotId.CollectIcon);

            // 创建活动分数数据
            ActScoreData actScoreData = new ActScoreData();
            actScoreData.Aid = ControllerManager.Get<CollectController>().GetCurrInfo().Aid;
            actScoreData.ActType = ActType.Collect;
            actScoreData.IncrScore = collectNum * SlotBet;

            DiceRollResult.ActData = new RepeatedField<ActScoreData>();
            DiceRollResult.ActData.Add(actScoreData);
            DiceRollResult.CollectNum = collectNum * SlotBet;

            // 触发收集任务
            TriggerCollectTasks();
        }
    }

    /// <summary>
    /// 清理已处理的收集版本
    /// </summary>
    private void ClearProcessedCollectVersions()
    {
        // 只保留还在待上报队列中的版本
        var versionsToKeep = new HashSet<long>();
        foreach (var kvp in _submitDataVersions)
        {
            if (WaitingSubmitSequence.Contains(kvp.Key))
            {
                versionsToKeep.Add(kvp.Value);
            }
        }

        _processedCollectVersions.IntersectWith(versionsToKeep);
    }
}
```

#### 3.3 修改主要流程

```csharp
private void RollOnAnim()
{
    // 记录上一次金币同步的事件戳
    LastSyncCoinsTs = ControllerManager.Get<UserController>().SyncCoinsTs;

    // 暂停投掷状态机
    PauseRollDice();
    // 消耗体力
    _userController.Spins -= _lastSlotBet;
    // 记录体力消耗
    TrackUtil.Consumespins(_lastSlotBet);

    // 获取当前投掷结果（带版本控制）
    DiceFaceArrayToPlay = GetCurDiceRusultWithVersion();

    // 处理奖励类型（包括收集数据）
    HandleRewardType(DiceFaceArrayToPlay);

    // 触发网络投掷事件
    EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);

    // 立即上报投掷结果
    SubmitRollResult();
}

// 修改上报成功处理
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    // ... 原有逻辑 ...

    if (code == 0) // 上报成功
    {
        // ... 原有处理 ...

        // 清理版本信息
        ClearSubmittedDataVersions();
        ClearProcessedCollectVersions();

        // 执行回调
        UpAction?.Invoke();
        ExecuteWaitingCallbacks();
    }
    else // 上报失败
    {
        // ... 原有处理 ...

        // 重置处理状态
        _processedCollectVersions.Clear();
        _submitDataVersions.Clear();

        ExecuteWaitingCallbacks();
    }
}
```

### 方案4：监控和调试工具

#### 4.1 添加状态监控

```csharp
public partial class DiceController
{
    /// <summary>
    /// 获取当前上报状态信息
    /// </summary>
    public string GetSubmitStatusInfo()
    {
        return $"上报状态: IsUping={IsUping}, " +
               $"待播放队列={DicePlayingSequence.Count}, " +
               $"待上报队列={WaitingSubmitSequence.Count}, " +
               $"等待回调队列={_waitingForSubmitCallbacks.Count}, " +
               $"数据版本={_dataVersion}";
    }

    /// <summary>
    /// 强制同步数据（调试用）
    /// </summary>
    public void ForceDataSync()
    {
        Log.Info("强制同步数据开始");

        // 清空所有本地状态
        WaitingSubmitSequence.Clear();
        _waitingForSubmitCallbacks.Clear();
        _processedCollectVersions.Clear();
        _submitDataVersions.Clear();
        IsUping = false;

        // 重新获取服务器数据
        RequestBeforeResults((serverList) =>
        {
            DicePlayingSequence = serverList;
            Log.Info($"强制同步完成，服务器队列长度: {serverList.Count}");
        }, null);
    }
}
```

#### 4.2 添加详细日志

```csharp
private void LogSubmitOperation(string operation, object data = null)
{
    var statusInfo = GetSubmitStatusInfo();
    var dataInfo = data != null ? $", 数据: {data}" : "";
    Log.Info($"[上报操作] {operation} - {statusInfo}{dataInfo}");
}
```

这个改进方案的核心优势：

1. **保持流畅性**：收集数据立即处理，用户体验不受影响
2. **确保完整性**：每次上报都包含所有待上报数据
3. **依赖等待**：所有需要服务器确认的操作都等待上报完成
4. **防重复处理**：通过版本控制避免数据重复处理
5. **数据一致性**：失败时重新同步服务器数据
6. **可监控调试**：提供状态查询和强制同步功能
```
