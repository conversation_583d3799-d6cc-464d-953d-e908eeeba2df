using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using CrazyCube.Protobuf.ActivityService;
using CrazyCube.Protobuf.CityService;
using CrazyCube.Protobuf.Public;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using Google.Protobuf.Collections;
using SkierFramework;
using TKFrame;
using UnityEngine;
using UnityEngine.UI;

#if GAME_DICE
public class VictoryMissionStageItem : UILoopScrollItem
{
    public GameObject ItemNode;
    public GameObject ItemBg1;
    public GameObject ItemBg2;
    public GameObject ItemBg3;
    public GameObject TaskNode;
    public Text TaskTxt;
    public Image TaskBg;
    public GameObject Finish;
    public Transform RewardNode;
    public GameObject Counter;
    public Text CounterText;
    public Slider ProgressSlider;
    public Transform RedManParent;
    public GameObject Max;
    public GameObject Hook;

    VictoryMissionController _victoryMissionController => ControllerManager.Get<VictoryMissionController>();
    PropController _propController => ControllerManager.Get<PropController>();
    ActivityController _activityController => ControllerManager.Get<ActivityController>();

    private RepeatedField<PrizeShow> _stageReward = new RepeatedField<PrizeShow>();//当前获取的奖励
    public MultiWinningMissionsRateBaseConf StageData;//显示阶段的数据
    private StringBuilder _sb = new StringBuilder();
    public bool IsNullItem = false;
    private List<GameObject> _hookList = new List<GameObject>();
    private int _progressBefore;
    private int _idx;
    
    public override void ScrollCellIndex(int idx)
    {
        base.ScrollCellIndex(idx);

        Listen<int, int>(EventType.VictoryMission.StageProgressAdd, OnStageProgressAdd);
        Listen<int>(EventType.VictoryMission.UpdateCounter, UpdateCouner);
        Listen<int>(EventType.VictoryMission.ResetStageProgress, ResetStageProgress);

        _idx = idx;

        //小人隐藏
        if (RedManParent.childCount > 0)
        {
            RedManParent.GetChild(0).localScale = Vector3.zero;
        }

        this.gameObject.name = idx.ToString();

        Max.SetActive(false);
        ItemNode.SetActive(true);

        //为了适配UI错位问题，第一个的高度为362
        RectTransform rectTransform = GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            // if(idx == 0)
            // {
            //     rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, 362);
            // }
            if(idx == _victoryMissionController.GetAllStageData().Count - 1)
            {
                rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, 302);
            }
            // else
            // {
            //     rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, 357);
            // }
        }

        //最后顶上的Item是占位（idx 0是最顶上），显示空的即可
        if(idx == 0)
        {
            ItemBg2.SetActive(false);
            ItemBg1.SetActive(false);
            ItemBg3.SetActive(false);
            ItemNode.SetActive(false);
            // IsNullItem = true;
            return;
        }
        //最下面的Item是占位，显示空树干
        else if(idx == _victoryMissionController.GetAllStageData().Count - 1)
        {
            ItemBg2.SetActive(true);
            ItemBg1.SetActive(false);
            ItemBg3.SetActive(false);
            ItemNode.SetActive(false);
            IsNullItem = true;

            //只有在玩家处于第一阶段的时候，最底下的占位Item才显示Counter
            if(_victoryMissionController.UserData.ThisStage == 1)
                SetCounterByNullItem();

            return;
        }
        else if(idx == 1)
        {
            ItemBg2.SetActive(false);
            ItemBg1.SetActive(false);
            ItemBg3.SetActive(true);
            //IsNullItem = true;
        }
        else
        {
            ItemBg2.SetActive(false);
            ItemBg1.SetActive(true);
            ItemBg3.SetActive(false);
            IsNullItem = false;
        }

        StageData = _victoryMissionController.GetAllStageData()[idx];

        //Log.Info($"ScroScrollCellIndex roundTaskConfig UpdateCouner idx: {idx} StageData.Stage: {StageData.Stage} _victoryMissionController.CurStageIndex: {_victoryMissionController.CurStageIndex}");
        //当前玩家自己所在的阶段的上一个阶段Item才显示Counter
        if(StageData.Stage == _victoryMissionController.CurStageIndex - 1)
        {
            TaskNode.SetActive(false);
            UpdateCouner((int)StageData.Stage);
            Counter.SetActive(true);
        }
        else
        {
            TaskNode.SetActive(true);
            Counter.SetActive(false);
        }

        Log.Info($"ScroScrollCellIndex roundTaskConfig idx: {idx} TaskRounds[index]:: {StageData.TaskRounds}");
        //Log.Info($"ScroScrollCellIndex roundTaskConfig idx: {idx} Prize[index]:: {StageData.Prize}");  
        _stageReward = StageData.Prize;
        // Log.Info($"ScroScrollCellIndex roundTaskConfig _victoryMissionController.GetAllStageData(): {_victoryMissionController.GetAllStageData()} _stageReward: {_stageReward}");
        SetReward();
        SetTaskNode();

        if(StageData.Stage == _victoryMissionController.UserData.ThisStage)
        {
            SetProgress();
            //如果是自己当前所处阶段，将小红人设置到此阶段下
            Trigger<int, Transform>(EventType.VictoryMission.UpdateRedManParent, (int)StageData.Stage, RedManParent);
        }
        else if(_victoryMissionController.IsFinishStage((int)StageData.Stage))
        {
            ProgressSlider.value = 1;
        }
        else
        {
            ProgressSlider.value = 0;
        }

        //Log.Info($"PlayProgressAnim ScrollCellIndex CurStageIndex: {_victoryMissionController.CurStageIndex} _victoryMissionController.GetAllStageData().Count: {_victoryMissionController.GetAllStageData().Count}");
    }

    private void SetReward()
    {
        RewardNode.transform.ClearAllChildren();
        if(IsNullItem || _idx == 1)
        {
            return;
        }
        PropSetting setting = new PropSetting();
        _propController.UpdateProps(_stageReward, 0.37f, RewardNode, setting, true, cb: (propBase) =>
        {
            Log.Info($"SetReward Creat Prop Finish By RewardNode");
        });
        bool isFinish = StageData.Stage < _victoryMissionController.UserData.ThisStage;
        Log.Info($"SetReward isFinish: {isFinish}");
        LoadHook(isFinish);
    }

    private void SetTaskNode()
    {
        TaskTxt.text = StageData.Stage.ToString();
        Log.Info($"SetTaskNode tast bg path: {_victoryMissionController.ActivityInfo.Theme + "_levelbg1"}");
        Log.Info($"SetTaskNode StageData.Stage: {StageData.Stage} IsFinishStage: {_victoryMissionController.IsFinishStage((int)StageData.Stage)}");
        if(_victoryMissionController.IsFinishStage((int)StageData.Stage))
        {
            ResUtil.LoadActivitySprite(TaskBg, _victoryMissionController.ActivityInfo.Theme + "_levelbg2", true);
            TaskTxt.gameObject.SetActive(false);
        }
        else
        {
            ResUtil.LoadActivitySprite(TaskBg, _victoryMissionController.ActivityInfo.Theme + "_levelbg1", true);
            TaskTxt.gameObject.SetActive(true);
            //TastBg.sprite = ResUtil.GetSprite(_victoryMissionController.ActivityInfo.Theme + "_img_level_bg2");
        }

        if(_victoryMissionController.IsMaxStage((int)StageData.Stage))
        {
            Max.SetActive(true);         
        }
        else
        {
            Max.SetActive(false);
        }
    }

    private void SetProgress()
    {
        _progressBefore = (int)_victoryMissionController.CurFinishTaskCount;
        //Log.Info($"SetProgress _victoryMissionController.CurFinishTaskCount: {_victoryMissionController.CurFinishTaskCount} (float)_victoryMissionController.GetStageTaskCount((int)StageData.Stage): {(float)_victoryMissionController.GetStageTaskCount((int)StageData.Stage)}");
        ProgressSlider.value = (float)_victoryMissionController.CurFinishTaskCount / (float)_victoryMissionController.GetStageTaskCount((int)StageData.Stage);
    }

    private void OnStageProgressAdd(int stageId, int addProgress)
    {
        if(!IsNullItem && StageData != null && stageId == (int)StageData.Stage)
        {
            PlayProgressAnim((float)_progressBefore, addProgress);
        }
    }

    /// <summary>
    /// 播放阶段任务进度动画
    /// </summary>
    /// <param name="progressBefore"></param>
    /// <param name="addProgress"></param>
    /// <returns></returns>
    private async Task PlayProgressAnim(float progressBefore, float addProgress)
    {
        //Log.Info($"PlayProgressAnim (float)_victoryMissionController.CurFinishTaskCount: {(float)_victoryMissionController.CurFinishTaskCount} addProgress: {addProgress}");
        await UniTask.NextFrame();
        float targetProgress = (float)_victoryMissionController.CurFinishTaskCount;
        float maxProgress = (float)_victoryMissionController.GetStageTaskCount((int)StageData.Stage);
        float end = Mathf.Min(targetProgress, maxProgress);
        await ProgressSlider.DOValue(end / maxProgress, 1f);
        Log.Info($"PlayProgressAnim end: {end} maxProgress: {maxProgress}");
        if(end >= maxProgress)
        {
            int lastStage = _victoryMissionController.CurStageIndex;
            _victoryMissionController.GetVictoryMissionRateReward((int)StageData.Stage, async (response) =>
            {
                //Log.Info($"PlayProgressAnim GetVictoryMissionRateReward CurStageIndex: {_victoryMissionController.CurStageIndex} _victoryMissionController.GetAllStageData().Count: {_victoryMissionController.GetAllStageData().Count}");
                //领取了最后一个阶段的奖励，因为GetAllStageData最后一个和第一个元素阶段是占位的空元素，所以需要-2

                Log.Info($"PlayProgressAnim GetVictoryMissionRateReward _victoryMissionController.GetAllStageData().Count - 2: {_victoryMissionController.GetAllStageData().Count - 2} CurStageIndex: {_victoryMissionController.CurStageIndex} lastStage: {lastStage}");

                if (_victoryMissionController.GetAllStageData().Count - 2 == lastStage)
                {
                    //Log.Info($"PlayProgressAnim GetVictoryMissionRateReward Last CurStageIndex: {_victoryMissionController.CurStageIndex}");
                    // TaskTxt.gameObject.SetActive(false);
                    // ResUtil.LoadActivitySprite(TaskBg, _victoryMissionController.ActivityInfo.Theme + "_levelbg2", true);
                    // Max.SetActive(false);

                    Trigger(EventType.VictoryMission.OnGetLastStageReward);
                }
                //非最后一个阶段的奖励
                else
                {
                    //Log.Info($"PlayProgressAnim GetVictoryMissionRateReward Not Last CurStageIndex: {_victoryMissionController.CurStageIndex}");
                    Trigger(EventType.VictoryMission.RedManFly, _victoryMissionController.CurStageIndex, RedManParent);
                    ResUtil.LoadActivitySprite(TaskBg, _victoryMissionController.ActivityInfo.Theme + "_levelbg2", true);
                    TaskTxt.gameObject.SetActive(false);
                    //RewardNode.transform.ClearAllChildren();
                    PlayHookAnim();
                }
            }, fail: () =>
            {
                _victoryMissionController.isPlayingAnim = false;
            });
        }
        else
        {
            //Trigger(EventType.VictoryMission.RedManIdel, _victoryMissionController.CurStageIndex);
            _victoryMissionController.isPlayingAnim = false;
        }
        //因为Counter挂在上一个阶段的Item上，所以需要-1
        Trigger(EventType.VictoryMission.UpdateCounter, _victoryMissionController.CurStageIndex - 1);
    }

    /// <summary>
    /// 重置阶段任务进度动画
    /// </summary>
    private async void ResetProgressAnin()
    {
        float maxProgress = (float)_victoryMissionController.GetStageTaskCount((int)StageData.Stage);
        float end = 0;
        await ProgressSlider.DOValue(end / maxProgress, 1f);
        UpdateCouner((int)StageData.Stage - 1);
    }

    /// <summary>
    /// 更新任务完成计数器
    /// 
    /// </summary>
    private void UpdateCouner(int stageId)
    {
        //Log.Info($"UpdateCouner stageId: {stageId} _idx: {_idx} _victoryMissionController.GetAllStageData().Count: {_victoryMissionController.GetAllStageData().Count} _victoryMissionController.UserData.ThisStage: {_victoryMissionController.UserData.ThisStage} IsNullItem: {IsNullItem}");
        //只有玩家处于第一阶段的时候，最底下的占位Item才显示Counter
        if(_idx == _victoryMissionController.GetAllStageData().Count - 1 && _victoryMissionController.UserData.ThisStage == 1)
        {
            //因为最后一个Item是占位所以需要特殊处理
            SetCounterByNullItem();
            return;
        }

        //如果stageId不等于当前Item的stage - 1，则不更新
        if(IsNullItem)
        {
            Counter.SetActive(false);
            return;
        }

        if(StageData == null)
        {
            return;
        }
        if(stageId == (int)StageData.Stage)
        {
            Counter.SetActive(true);
        }
        else
        {
            Counter.SetActive(false);
            return;
        }

        //因为显示的是当前Item下一个阶段的进度所以+1
        int stage = (int)StageData.Stage + 1;
        int taskCount = (int)_victoryMissionController.GetStageTaskCount(stage);

        _sb.Clear();
        _sb.AppendFormat($"{_victoryMissionController.CurFinishTaskCount}/{taskCount}");
        CounterText.text = _sb.ToString();

        //Log.Info($"UpdateCouner UserData.CurFinishStage.Count: {_victoryMissionController.UserData.CurFinishStage.Count} CurStageIndex: {_victoryMissionController.CurStageIndex}");
        //已经通关
        // if(_victoryMissionController.UserData.CurFinishStage.Count == _victoryMissionController.CurStageIndex)
        // {
        //     ResUtil.LoadActivitySprite(TaskBg, _victoryMissionController.ActivityInfo.Theme + "_levelbg2", true);
        //     TaskTxt.gameObject.SetActive(false);
        //     Counter.SetActive(false);
        //     Max.SetActive(false);
        //     return;
        // }
    }

    
    /// <summary>
    /// 占位阶段设置计数器
    /// </summary>
    private void SetCounterByNullItem()
    {
        //因为是第一个，恒定stage为1
        int stage = 1;
        int taskCount = (int)_victoryMissionController.GetStageTaskCount(stage);

        _sb.Clear();
        _sb.AppendFormat($"{_victoryMissionController.CurFinishTaskCount}/{taskCount}");
        CounterText.text = _sb.ToString();
        Counter.SetActive(true);
    }

    private async void LoadHook(bool isFinish)
    {
        _hookList.Clear();
        await UniTask.Delay(20);
        for(int i = 0; i < RewardNode.childCount; i++)
        {
            GameObject hook = Instantiate(Hook, RewardNode.GetChild(i));
            hook.transform.localPosition = Vector3.zero;
            hook.transform.localScale = new Vector3(2.5f, 2.5f, 2.5f);
            hook.SetActive(false);
            _hookList.Add(hook);
            if(isFinish)
            {
                hook.SetActive(true);
            }
        }
    }

    private void PlayHookAnim()
    {
        for(int i = 0; i < _hookList.Count; i++)
        {
            AnimTools.Play(_hookList[i].GetComponent<Animation>(), "am_dagou");
            _hookList[i].SetActive(true);
        }
    }

    private void ResetStageProgress(int stageId)
    {
        Log.Info($"VictoryMissionStageItem ResetStageProgress stageId: {stageId} _idx: {_idx} IsNullItem: {IsNullItem} _victoryMissionController.GetAllStageData().Count: {_victoryMissionController.GetAllStageData().Count}");


        //如果是最顶上占位item，不处理
        if(IsNullItem && _idx != _victoryMissionController.GetAllStageData().Count - 1)
        {
            return;
        }

        if(StageData == null)
        {
            return;
        }

        Log.Info($"VictoryMissionStageItem ResetStageProgress 02 stageId: {stageId} _idx: {_idx} (int)StageData.Stage: { (int)StageData.Stage} _victoryMissionController.GetAllStageData().Count: {_victoryMissionController.GetAllStageData().Count}");
        //当前阶段播放进度重置动画
        if(stageId == (int)StageData.Stage)
        {
            ResetProgressAnin();
            return;
        }

        if(_idx != _victoryMissionController.GetAllStageData().Count - 1 && stageId - 1 == (int)StageData.Stage)
        {
            UpdateCouner((int)StageData.Stage);
        }
         //如果是最底下的占位Item，并且stageId为1，则特殊处理
        else if(stageId == 1 && _idx == _victoryMissionController.GetAllStageData().Count - 1)
        {
            UpdateCouner(1);
        }
    }
}
#endif