---
description: 
globs: 
alwaysApply: false
---
# Unity 网络通信规范

## 网络核心
- **网络控制器**: `NetworkController` 是网络通信的核心，负责消息的收发和网络服务的生命周期管理。
- **消息回调**: 使用 `networkController.AddRecvCallback` 和 `RemoveRecvCallback` 注册和注销特定消息的处理函数。
- **发送消息**: 通过 `networkController.SendMsg` 发送消息。

## 数据协议
- **序列化**: 网络通信统一使用 Protobuf 进行数据序列化和反序列化。
- **辅助工具**: 使用 `ProtobufUtil.Parser<T>` 将字节数组安全地解析为 Protobuf 对象。

## 异常与重试
- **通用异常**: 网络请求的异步操作通过 `try-catch` 捕获常规异常。
- **业务重试**: 项目中存在特定的业务重试机制，例如登录失败时使用 `DialogUtils.ShowLoginRetryDialog` 或 `UIManager.Instance.Open(UIType.RetryDialog, retryFun)` 弹窗提示用户重试。

## 异步处理
- 网络请求必须使用 `async/await` 和 `UniTask` 进行异步处理，避免阻塞。
- 必须包含 `try-catch` 异常处理机制，妥善处理网络超时、断开等异常。

## 典型用法
- 发送请求：`await NetworkManager.Instance.SendAsync(request);`
- 错误处理：`try { ... } catch (Exception e) { Log.Error(e); }`

---
详细实现见 `NetworkController` 及 `LoginController` 等。



