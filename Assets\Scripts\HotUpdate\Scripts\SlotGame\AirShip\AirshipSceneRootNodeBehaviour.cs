﻿using CrazyCube.Protobuf.GameService;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using MelenitasDev.SoundsGood;
using SkierFramework;
using System.Collections.Generic;
using System.Threading;
using TKFrame;
using UnityEngine;
public class AirshipSceneRootNodeBehaviour : TKViewBehaviour
{
    public Transform RewardShipParent;
    [Header("格子下落特效")]
    public ParticleSystem effectCompleteDrop;
    [Header("格子解锁特效")]
    public ParticleSystem effectUnlock;
    public Transform PassLevelCameraParentNode;
    public Transform EnterpriseGridCameraParentNode;
    public Transform EnterpriseFreeMoveCameraParentNode;
    public AirshipBankBehaviour AirshipBankBehaviour;
    public GridRewardBehaviour RewardBehaviour;//收益 
    public AnimationCurve BuildAnimCurve;
    [Header("飞机起飞相机")]
    public Transform AirPlaneStartNode;
    public AirshipConfigBehaviour AirshipConfigBehaviour;
    public AirShipNPCBehaviour AirShipNPCBehaviour;
    private float cameraOldFov = 30;
    private Vector3 cameraOldPos = Vector3.zero;
    private Vector3 offsetGridPasselev = new Vector3(279.91f, 315.75f, 102.64f);//过关播放解锁&完成动画时的相对位置
    private Vector3 offsetGridChangeScene = new Vector3(111.28f, 139.34f, 38.26f);//相机与格子的拉近转场时的相对位置
    private int _passLevelAniLevel;//转化后的格子等级   
    private AirshipBehaviour _curShipBehaviour;//当前展示的经营船  
    private Sequence _enterpriseCameraMoveSequence;
    private CancellationTokenSource _rewardCameraAniCancelTokenSource;
    private Dictionary<int, AirshipBehaviour> _rewardShipsDic = new Dictionary<int, AirshipBehaviour>();//经营船   
    private AirshipController _airshipController => ControllerManager.Get<AirshipController>();
    private CameraController _cameraController => ControllerManager.Get<CameraController>();
    private PassLevelController _passLevelController => ControllerManager.Get<PassLevelController>();
    private BuildController _buildController => ControllerManager.Get<BuildController>();
    private GuideController _guideController => ControllerManager.Get<GuideController>();
    private UserController _userController => ControllerManager.Get<UserController>();
    private AirshipConfig _airshipConfig = ModelManager.Get<AirshipConfig>();
    private bool isEnd = false;//是否需要换船

    private AirShipBagBehaviour AirShipBagBehaviour;
    private AirShipCloudBehaviour AirShipCloud;
    private Vector3 initPos;

    protected override async void InternalAwake()
    {
        base.InternalAwake();
        initPos = EnterpriseFreeMoveCameraParentNode.transform.position;
        Listen<CameraSceneType, CameraSceneType>(EventType.MainCamera.SwitchCameraSceneAniStart, OnSwithSceneCameraStart);
        Listen<CameraSceneType, CameraSceneType>(EventType.MainCamera.SwitchCameraSceneAniEnd, OnSwithSceneCameraEnd);
        Listen(EventType.PassLevel.PassLevelStartToShip, PassLevelStartToShip);
        Listen(EventType.Airship.DoubleClick, OnDoubleClick);
        Listen(EventType.NoviceGuide.MoveBankGuidCam, MoveBankGuidCamAsync);
        Listen(EventType.NoviceGuide.ResumeBankGuidCam, OnResumeBankGuidCam);
        Listen(EventType.NoviceGuide.CloseEnterpriseUnlockGuideStepClick, OnPlayAirshipPassLevelAnimation);
        Listen(EventType.Airship.RefreshSkin, ShowChangeSkinEffect);
        Listen(EventType.Airship.MapAniOver, MapAniOver);
        Listen(EventType.Airship.ShowNpcDownShipAnimation, ShowNpcDownShipAnimation);
        Listen(EventType.NoviceGuide.ClickShip1OverStep5, OpenShipMapView);
        Listen(EventType.Airship.Ship1OverGuideOver, MovePassLevelGrid);
        _airshipController.BuildAnimCurve = BuildAnimCurve;
        effectCompleteDrop.gameObject.SetActive(false);
        effectUnlock.gameObject.SetActive(false);

        //加载背包
        var bag = await _airshipController.LoadBag(transform);
        AirShipBagBehaviour = bag.GetComponent<AirShipBagBehaviour>();
        AirShipBagBehaviour.gameObject.SetActive(false);

        //加载默认背景
        await LoadShip(1, $"Ship1_{PathConst.AirShip.Bg}{1}", transform.GetChild(0), _skinBG);
        if (_skinBG.ContainsKey(1))
        {
            AirShipCloud = _skinBG[1].GetComponent<AirShipCloudBehaviour>();
        }
    }

    protected override void InternalStart()
    {
        base.InternalStart();
        _cameraController.MainCamera.SetCameraSceneNode(CameraSceneType.AirShipSteal, AirshipConfigBehaviour.StealComeStartNode.transform);
        _cameraController.MainCamera.SetLightSceneNode(LightSceneType.Airship, AirshipConfigBehaviour.AirshipLight);
        _cameraController.MainCamera.SetEnvironMentLightSetting(LightSceneType.Airship, new EnvironmentLightSetting
        {
            skyColor = AirshipConfigBehaviour.skyColor,
            groundColor = AirshipConfigBehaviour.groundColor,
            equatorColor = AirshipConfigBehaviour.equatorColor,
            ambientMode = AirshipConfigBehaviour.ambientLightMode
        });
        AirshipConfigBehaviour.AirshipLight.gameObject.SetActive(false);
    }

    private async UniTask<AirshipBehaviour> GetCurRewardShip(bool isCheckSkin = true)
    {
        ResetOldShip(_curShipBehaviour);
        var index = _airshipController.GetShipIndex(_airshipController.CurShowShipID);
        var curSkinID = _airshipController.CurSkinID;
        if (isCheckSkin)//展示经营船时检查皮肤
        {
            if (curSkinID > 1)
            {
                SetSkinBackGround(curSkinID);
                if (_skinShipsDic.ContainsKey(curSkinID))
                    return _skinShipsDic[curSkinID];
                else
                {
                    await LoadShip(curSkinID, _airshipConfig.FindAirShipResourceBySkinId(curSkinID), RewardShipParent, _skinShipsDic);
                    if (_skinShipsDic.ContainsKey(curSkinID))
                    {
                        _skinShipsDic[curSkinID].ResetPos();
                        _skinShipsDic[curSkinID].SetGridRewardBehaviour(RewardBehaviour);
                        return _skinShipsDic[curSkinID];
                    }
                }
            }
        }
        SetSkinBackGround(index);
        if (_rewardShipsDic.ContainsKey(index))
            return _rewardShipsDic[index];
        await LoadShip(index, $"Ship{index}_Feichuan{index}", RewardShipParent, _rewardShipsDic);
        if (_rewardShipsDic.ContainsKey(index))
        {
            _rewardShipsDic[index].ResetPos();
            _rewardShipsDic[index].SetGridRewardBehaviour(RewardBehaviour);
            return _rewardShipsDic[index];
        }
        Log.Error($"[AirshipSceneRootNodeBehaviour]没有找到船:{index}");
        return null;
    }

    private void ResetOldShip(AirshipBehaviour airship)
    {
        if (airship == null) return;
        airship.gameObject.SetActive(false);
        airship.ResetPos();
    }

    private async UniTask LoadShip<T>(int index, string path, Transform parent, Dictionary<int, T> dic) where T : Component
    {
        if (dic.ContainsKey(index))
            return;
        var go = await AirshipController.LoadShipPref(path, parent);
        if (go == null) return;
        if (!dic.ContainsKey(index))
        {
            var ship = go.GetComponent<T>();
            ship.gameObject.SetActive(false);
            dic.Add(index, ship);
        }
        else
        {
            GameObject.Destroy(go.gameObject);
        }
    }

    /// <summary>
    /// 刷新过关船
    /// </summary> 
    public async UniTask RefreshPassLevelShip(int level, bool isComplete = false)
    {
        _airshipController.CurSkinID = 0;
        _airshipController.CurDoorID = 0;
        _airshipController.CurBankID = 0;
        _curShipBehaviour = await GetCurRewardShip(false);
        if (!isComplete)
        {
            _passLevelAniLevel = level;
        }
        _curShipBehaviour.ResetPos();
        _curShipBehaviour.gameObject.SetActive(true);
        _curShipBehaviour.RefreshPassLevelShip(level, isComplete);
        SetSkin();
        _cameraController.MainCamera.SetCameraSceneNode(CameraSceneType.AirShipEnterprise, AirshipConfigBehaviour.FarCameraNode.transform);
    }

    private void SetSkin()
    {
        Log.Info($"[AirshipSceneRootNodeBehaviour]当前展示的皮肤id:{_airshipController.CurSkinID}");
        Log.Info($"[AirshipSceneRootNodeBehaviour]当前展示的大门id:{_airshipController.CurDoorID}");
        Log.Info($"[AirshipSceneRootNodeBehaviour]当前展示的银行id:{_airshipController.CurBankID}");
        var index = _airshipController.GetShipIndex(_airshipController.CurShowShipID);
        //大门
        if (_airshipController.CurDoorID > 1)
        {
            var path = _airshipConfig.FindAirShipResourceBySkinId(_airshipController.CurDoorID);
            _curShipBehaviour.SetDoorSkin(path, path);
        }
        else
        {
            var name = $"{PathConst.AirShip.Door}{index}";
            _curShipBehaviour.SetDoorSkin($"Ship{index}_{name}", name);
        }
        //银行
        if (_airshipController.CurBankID > 1)
        {
            var path = _airshipConfig.FindAirShipResourceBySkinId(_airshipController.CurBankID);
            _curShipBehaviour.SetBankSkin(path, path, SetBagNode);
        }
        else
        {
            var name = $"{PathConst.AirShip.Bank}{index}";
            _curShipBehaviour.SetBankSkin($"Ship{index}_{name}", name, SetBagNode);
        }
    }

    /// <summary>
    /// 刷新经营船
    /// </summary>
    public async UniTask RefreshRewardShip(long friendLevel = 0)
    {
        if (_airshipController.CurAirshipType == AirshipType.OtherRewardAirship)
        {
            _airshipController.OtherShipEquipData.TryGetValue(CosmeticPosition.ShipCosmetic.ToString(), out long skin);
            _airshipController.OtherShipEquipData.TryGetValue(CosmeticPosition.ShipGateCosmetic.ToString(), out long door);
            _airshipController.OtherShipEquipData.TryGetValue(CosmeticPosition.ShipBankCosmetic.ToString(), out long bank);
            _airshipController.CurSkinID = (int)skin;
            _airshipController.CurDoorID = (int)door;
            _airshipController.CurBankID = (int)bank;
        }
        else
        {
            if (IsNeedShowChangeSkinEffect())//换船动画展示之前的模型
            {
                int.TryParse(SaveUtils.LoadRemoteUser(StorageKey.Airship.ShowSkinAnim, "0"), out int skin);
                int.TryParse(SaveUtils.LoadRemoteUser(StorageKey.Airship.ShowDoorAnim, "0"), out int door);
                int.TryParse(SaveUtils.LoadRemoteUser(StorageKey.Airship.ShowBankAnim, "0"), out int bank);
                _airshipController.CurSkinID = skin;
                _airshipController.CurDoorID = door;
                _airshipController.CurBankID = bank;
            }
            else
            {
                _airshipController.CurSkinID = (int)_airshipController.GetSkinIdByPosition(CosmeticPosition.ShipCosmetic);
                _airshipController.CurDoorID = (int)_airshipController.GetSkinIdByPosition(CosmeticPosition.ShipGateCosmetic);
                _airshipController.CurBankID = (int)_airshipController.GetSkinIdByPosition(CosmeticPosition.ShipBankCosmetic);
                SaveSkinInfo();
            }
        }

        _curShipBehaviour = await GetCurRewardShip();
        _curShipBehaviour.ResetPos();
        _curShipBehaviour.gameObject.SetActive(true);
        if (_airshipController.CurAirshipType == AirshipType.OtherRewardAirship)
        {
            _curShipBehaviour.RefreshOtherRewardShip(friendLevel);
            AirshipBankBehaviour.SetShipCam(_curShipBehaviour.StealLightBeginCam, _curShipBehaviour.StealLightEndCam);
        }
        else
        {
            _curShipBehaviour.RefreshOwnRewardShip();
        }
        SetSkin();
        InitFreeMoveVectors();
        if (_cameraController.CameraSceneType == CameraSceneType.AirShipEnterprise &&
           _airshipController.CurAirshipType == AirshipType.OwnRewardAirship)
        {
            AirShipNPCBehaviour.NpcMove(_curShipBehaviour.LevelNodes);
        }
    }

    //过关的格子对应的索引（0，1，2，3）
    private int nodeIndex => (_passLevelAniLevel - 1) % AirshipController.ShipCount;
    //过关后回到经营场景，镜头聚焦当过关的格子
    private async void PassLevelStartToShip()
    {
        Log.Info($"[AirshipSceneRootNodeBehaviour]MovePassLevelCameraNodeToOldGrid:nodeIndex:{nodeIndex}");
        PassLevelCameraParentNode.transform.rotation = AirshipConfigBehaviour.FarCameraNode.rotation;
        PassLevelCameraParentNode.transform.position = AirshipConfigBehaviour.FarCameraNode.transform.position;
        await _cameraController.MainCamera.SetTempParentNode(PassLevelCameraParentNode);
        effectCompleteDrop.transform.position = _curShipBehaviour.LevelNodes[nodeIndex].transform.position;
        effectCompleteDrop.transform.localPosition += Vector3.up * 6;
        effectCompleteDrop.gameObject.SetActive(true);
        effectCompleteDrop.Play();
        PassLevelCameraParentNode.transform.DORotate(new Vector3(45, -110, 0), 1f).ToUniTask().Forget();
        _ = PassLevelCameraParentNode.DOMove(_curShipBehaviour.LevelNodes[nodeIndex].transform.position + offsetGridPasselev, 1f);
        await UniTask.Delay(500);
        await PlayCurGridComplete(nodeIndex);
        if (!_guideController.IsSkipGuid &&
           SaveUtils.LoadRemoteUser(StorageKey.NoviceGuide.IsEnterpriseUnlockRewardGuideEntered, "false") == "false")
        {
            _guideController.EnterpriseUnlockGuideFlow();
        }
        else
        {
            OnPlayAirshipPassLevelAnimation();
        }
    }

    //播放格子完成动画
    private async void OnPlayAirshipPassLevelAnimation()
    {
        Log.Info($"[AirshipSceneRootNodeBehaviour]PlayCurGridComplete over:isEnd:{isEnd}");
        if (isEnd)
        {
            var IsShip1OverGuideEntered = SaveUtils.LoadRemoteUser(StorageKey.NoviceGuide.IsShip1OverGuideEntered, "false");
            if (IsShip1OverGuideEntered == "false")
            {
                AirShipBagBehaviour.gameObject.SetActive(true);
                AirShipBagBehaviour.PlaySuoLoop();
            }
            await PassLevNodeMoveNear();
            //已到最大等级，结束过关流程
            if (_buildController.IsMaxLev)
            {
                PassLevelOverFlow();
                return;
            }
            else
            {
                if (IsShip1OverGuideEntered == "false")
                {
                    AirShipBagBehaviour.gameObject.SetActive(true);
                    _guideController.CurBuildGridObj = AirShipBagBehaviour.gameObject;
                    await AirShipBagBehaviour.PlayLockShip();
                    await UniTask.Delay(500);
                    _guideController.Ship1OverGuideFlow();
                }
                else
                {
                    OpenShipMapView();
                }
            }
        }
        else
        {
            MovePassLevelGrid();
        }
    }

    //换船时的地图移动动画播放完成
    private async void MapAniOver()
    {
        if (_guideController.IsShip1OverGuideOn)
        {
            await PassLevNodeMoveNear();
            _guideController.ChangeGuideStep<Ship1GuideStep6State>();
        }
        else
        {
            MovePassLevelGrid();
        }
    }

    //小人下船动画
    private async void ShowNpcDownShipAnimation()
    {
        //相机对准飞机 
        PassLevelCameraParentNode.transform.DORotateQuaternion(AirPlaneStartNode.rotation, 0.5f).ToUniTask().Forget();
        _ = PassLevelCameraParentNode.DOMove(AirPlaneStartNode.transform.position, 0.5f);
        //飞机动画
        AirShipNPCBehaviour.PlayPlaneStart();
        await UniTask.Delay(2000);
        UIManager.Instance.Open(UIType.DownShipEffectView);
        await UniTask.Delay(3500);
        await PassLevNodeMoveNear();

        _curShipBehaviour.PlayGetCoin(0);
        await UniTask.Delay(1000);
        _guideController.ChangeGuideStep<Ship1GuideStep3State>();
    }

    //解锁新格子，结束过关流程
    private async void MovePassLevelGrid()
    {
        await MovePassLevelCameraNodeToNewGrid(_curShipBehaviour, nodeIndex + 1);
        await PlayUnlockNewGrid();
        PassLevelOverFlow();
    }

    /// <summary>
    /// 过关镜头移动到新格子
    /// </summary> 
    private async UniTask MovePassLevelCameraNodeToNewGrid(AirshipBehaviour airshipBehaviour, int index)
    {
        if (airshipBehaviour.LevelNodes.Count <= index || isEnd)
            index = 0;
        Log.Info($"[AirshipSceneRootNodeBehaviour]MovePassLevelCameraNodeToNewGrid:index:{index}");
        PassLevelCameraParentNode.transform.DORotate(new Vector3(45, -110, 0), 1f).ToUniTask().Forget();
        await PassLevelCameraParentNode.DOMove(airshipBehaviour.LevelNodes[index].transform.position + offsetGridPasselev, 1f);
    }

    private async UniTask MovePassLevelToBuildScene(AirshipBehaviour airshipBehaviour, int index)
    {
        if (airshipBehaviour.LevelNodes.Count <= index)
            index = 0;
        Log.Info($"[AirshipSceneRootNodeBehaviour]MovePassLevelToBuildScene:index:{index}");
        await _cameraController.MainCamera.transform.parent.DOMove(airshipBehaviour.LevelNodes[index].transform.position + offsetGridChangeScene, 1);
        PauseClouds();
    }

    //结束过关流程
    private async void PassLevelOverFlow()
    {
        Log.Info($"[AirshipSceneRootNodeBehaviour]PassLevelOverFlow:nodeIndex:{nodeIndex}");
        Trigger(EventType.MainView.FadeOut);
        _ = MovePassLevelToBuildScene(_curShipBehaviour, nodeIndex + 1);//相机拉近当前建造格子，白光转场
        Trigger<CameraSceneType>(EventType.MainCamera.SwitchCameraScene, CameraSceneType.Slot);
        await UniTask.Delay(800);
        UIManager.Instance.Close(UIType.WhiteGlowView);
        Trigger(EventType.MainView.FadeIn);
        //因为系统解锁的缘故这里再赋值
        ControllerManager.Get<SystemUnlockController>().TempLevel = ControllerManager.Get<UserController>().LevelId;
    }

    /// <summary>
    ///格子完成动画
    /// </summary> 
    public async UniTask PlayCurGridComplete(int index)
    {
        Log.Info($"[AirshipSceneRootNodeBehaviour]PlayCurGridComplete:index:{index},LevelNodes:{_curShipBehaviour.LevelNodes.Count}");
        AudioManager.PlaySound(SoundDef.ui_ship_square_finish);
        await _curShipBehaviour.LevelNodes[index].PlayGridComplete();
        await UniTask.Delay(600);
        isEnd = _passLevelAniLevel % AirshipController.ShipCount == 0;
        isPlayGridCompleteOver = false;
        if (isEnd)//一条船建造完毕同步收益数据
        {
            _airshipController.RequestOwnShipDetail(() =>
            {
                PlayGridCompleteOver();

            }, PlayGridCompleteOver);
        }
        else
        {
            PlayGridCompleteOver();
        }
        await UniTask.WaitUntil(() => isPlayGridCompleteOver);
    }

    bool isPlayGridCompleteOver = false;
    private void PlayGridCompleteOver()
    {
        effectCompleteDrop.gameObject.SetActive(false);
        isPlayGridCompleteOver = true;
    }

    /// <summary>
    /// 格子解锁动画
    /// </summary> 
    public async UniTask PlayUnlockNewGrid()
    {
        AudioManager.PlaySound(SoundDef.ui_ship_square_unlock);
        int index = nodeIndex + 1;
        if (index >= _curShipBehaviour.LevelNodes.Count)
            index = 0;
        Log.Info($"[AirshipSceneRootNodeBehaviour]PlayUnlockNewGrid index:{index}");
        await _curShipBehaviour.LevelNodes[index].PlayUnlockGrid(() => { PlayUnlockEffect(index); });
        await UniTask.Delay(500);
        effectUnlock.gameObject.SetActive(false);
        if (_guideController.IsShip1OverGuideOn)
        {
            PassLevelOverFlow();
        }
    }

    private void PlayUnlockEffect(int index)
    {
        effectUnlock.transform.position = _curShipBehaviour.LevelNodes[index].transform.position;
        effectUnlock.gameObject.SetActive(true);
        effectUnlock.Play();
    }

    #region 一键领取
    public void OnDoubleClick()
    {
        if (_rewardCameraAniCancelTokenSource == null) return;
        _rewardCameraAniCancelTokenSource.Cancel();
        _rewardCameraAniCancelTokenSource = null;
        SkipToRewardCameraMoveAniEndState();
    }

    public async UniTask RewardCameraMoveAni()
    {
        SaveCameraData();
        _cameraController.MainCamera.transform.DOKill();

        //初始化 CancellationTokenSource
        _rewardCameraAniCancelTokenSource = new CancellationTokenSource();
        CancellationToken token = _rewardCameraAniCancelTokenSource.Token;

        Trigger(EventType.Airship.PlayButtomOutAnim);
        if (_enterpriseCameraMoveSequence != null)
        {
            _enterpriseCameraMoveSequence.Kill();
            _enterpriseCameraMoveSequence = DOTween.Sequence();
        }
        else
        {
            _enterpriseCameraMoveSequence = DOTween.Sequence();
        }

        EnterpriseGridCameraParentNode.transform.position = EnterpriseFreeMoveCameraParentNode.transform.position;
        EnterpriseGridCameraParentNode.transform.rotation = AirshipConfigBehaviour.FarCameraNode.rotation;

        if (_airshipController.CurCameraStatus != CameraStatus.RewardMoveCamera)
        {
            _airshipController.CurCameraStatus = CameraStatus.RewardMoveCamera;
            await _cameraController.MainCamera.SetTempParentNode(EnterpriseGridCameraParentNode, -1, true, 0.15f, token: token).SuppressCancellationThrow();
        }

        int airshipLength = _curShipBehaviour.AirshipHullsCount;

        float moveTime = 100 * airshipLength * 2f / 1000;
        float time = 0.5f;

        Vector3 shortestAirshipCameraPos = _curShipBehaviour.RewardStartCameraNode2.transform.position;
        Vector3 longestAirshipCameraPos = _curShipBehaviour.RewardStartCameraNode20.transform.position;

        Vector3 shortestAirshipCameraEular = _curShipBehaviour.RewardStartCameraNode2.transform.rotation.eulerAngles;
        Vector3 longestAirshipCameraEular = _curShipBehaviour.RewardStartCameraNode20.transform.rotation.eulerAngles;

        float shortestAirshipCameraFov = _curShipBehaviour.RewardStartCameraNode2.transform.GetComponent<Camera>().fieldOfView;
        float longestAirshipCameraFov = _curShipBehaviour.RewardStartCameraNode20.transform.GetComponent<Camera>().fieldOfView;

        Vector3 realAirshipCameraPos = shortestAirshipCameraPos + (longestAirshipCameraPos - shortestAirshipCameraPos) * airshipLength * 0.1f;
        Vector3 realAirshipCameraEular = shortestAirshipCameraEular + (longestAirshipCameraEular - shortestAirshipCameraEular) * airshipLength * 0.1f;
        float realAirshipCameraFov = shortestAirshipCameraFov + (longestAirshipCameraFov - shortestAirshipCameraFov) * airshipLength * 0.1f;
        Quaternion quaternion = Quaternion.Euler(realAirshipCameraEular);
        await _enterpriseCameraMoveSequence
                 .Append(EnterpriseGridCameraParentNode.DOMove(realAirshipCameraPos, time).SetEase(Ease.OutQuart))
                 .Join(EnterpriseGridCameraParentNode.DORotateQuaternion(quaternion, time).SetEase(Ease.OutQuart))
                 .Join(UIManager.Instance.MainCamera.GetComponent<Camera>().DOFieldOfView(realAirshipCameraFov, time))
                 .ToUniTask(cancellationToken: token).SuppressCancellationThrow();

        await _enterpriseCameraMoveSequence
                  .Append(EnterpriseGridCameraParentNode.DOMove(shortestAirshipCameraPos, time).SetEase(Ease.OutQuart))
                  .ToUniTask(cancellationToken: token).SuppressCancellationThrow();

        await UniTask.WaitForSeconds(time, cancellationToken: token).SuppressCancellationThrow();
        // RewardBehaviour.ShowAllGirdRewards(token: token).Forget();

        await _enterpriseCameraMoveSequence
            .Append(EnterpriseGridCameraParentNode.DOMove(_curShipBehaviour.RewardEndCameraNode.transform.position, moveTime))
            .Join(EnterpriseGridCameraParentNode.DORotateQuaternion(_curShipBehaviour.RewardEndCameraNode.transform.rotation, moveTime))
            .Join(UIManager.Instance.MainCamera.GetComponent<Camera>().DOFieldOfView(_curShipBehaviour.RewardEndCameraNode.GetComponent<Camera>().fieldOfView, moveTime))
            .ToUniTask(cancellationToken: token).SuppressCancellationThrow();

        await UniTask.WaitForSeconds(moveTime - 0.2f, cancellationToken: token).SuppressCancellationThrow();

        _rewardCameraAniCancelTokenSource = null;
    }

    public async void SkipToRewardCameraMoveAniEndState()
    {
        if (_enterpriseCameraMoveSequence != null)
        {
            _enterpriseCameraMoveSequence.Kill();
        }
        int airshipLength = _curShipBehaviour.AirshipHullsCount;

        RewardBehaviour.HideGridHead();
        float shortestAirshipCameraFov = _curShipBehaviour.RewardStartCameraNode2.transform.GetComponent<Camera>().fieldOfView;
        float longestAirshipCameraFov = _curShipBehaviour.RewardStartCameraNode20.transform.GetComponent<Camera>().fieldOfView;

        float realAirshipCameraFov = shortestAirshipCameraFov + (longestAirshipCameraFov - shortestAirshipCameraFov) * airshipLength * 0.1f;
        UIManager.Instance.MainCamera.GetComponent<Camera>().fieldOfView = realAirshipCameraFov;

        EnterpriseGridCameraParentNode.position = _curShipBehaviour.RewardEndCameraNode.transform.position;
        EnterpriseGridCameraParentNode.rotation = _curShipBehaviour.RewardEndCameraNode.transform.rotation;

        _airshipController.CurCameraStatus = CameraStatus.RewardMoveCamera;
        await _cameraController.MainCamera.SetTempParentNode(EnterpriseGridCameraParentNode, -1, true);
    }
    #endregion

    #region Camera
    public async UniTask RewardCameraMoveBack()
    {
        Trigger(EventType.Airship.PlayButtomInAnim);
        if (_airshipController.CurCameraStatus != CameraStatus.RewardMoveCamera) return;
        await CameraMoveBackToOldPos();
    }

    private void SaveCameraData()
    {
        if (_airshipController.CurCameraStatus == CameraStatus.FreeMoveCamera)
        {
            cameraOldPos = _cameraController.MainCamera.transform.position;
            cameraOldFov = UIManager.Instance.MainCamera.GetComponent<Camera>().fieldOfView;
        }
    }

    private async UniTask CameraMoveBackToOldPos()
    {
        Log.Info("[AirshipSceneRootNodeBehaviour]CameraMoveBackToOldPos");
        UIManager.Instance.MainCamera.GetComponent<Camera>().fieldOfView = cameraOldFov;
        EnterpriseFreeMoveCameraParentNode.transform.position = cameraOldPos;
        await _cameraController.MainCamera.SetTempParentNode(EnterpriseFreeMoveCameraParentNode, -1, true, 0.5f);

        _airshipController.CurCameraStatus = CameraStatus.FreeMoveCamera;
    }

    //经营船下自由视角
    private void InitFreeMoveVectors(AirshipBehaviour ship = null)
    {
        if (ship == null)
        {
            ship = _curShipBehaviour;
        }
        if (ship == null || AirshipConfigBehaviour == null || _passLevelController.IsPassingLevel)
            return;
        EnterpriseFreeMoveCameraParentNode.rotation = AirshipConfigBehaviour.FarCameraNode.rotation;
        _cameraController.MainCamera.SetCameraSceneNode(CameraSceneType.AirShipEnterprise, EnterpriseFreeMoveCameraParentNode.transform);
    }

    private async void OnSwithSceneCameraStart(CameraSceneType startSceneType, CameraSceneType targetSceneType)
    {
        //切换场景关闭弱引导
        if (UIManager.Instance.IsOpen(UIType.AirShipFreeGuideDialog))
        {
            UIManager.Instance.Close(UIType.AirShipFreeGuideDialog);
        }
        if (UIManager.Instance.IsOpen(UIType.FreeGuideDialog))
        {
            UIManager.Instance.Close(UIType.FreeGuideDialog);
        }
        if (startSceneType == CameraSceneType.AirShipEnterprise)
        {
            AirShipBagBehaviour.gameObject.SetActive(false);
            AirShipNPCBehaviour.HideNpc();
            if (_airshipController.CurAirshipType == AirshipType.OwnRewardAirship)
            {
                RewardBehaviour.SaveRemoteReward();
            }
            await UniTask.Delay(1000);
            EnterpriseFreeMoveCameraParentNode.transform.position = initPos;
        }
    }

    private async void OnSwithSceneCameraEnd(CameraSceneType startSceneType, CameraSceneType targetSceneType)
    {
        if (startSceneType == CameraSceneType.AirShipEnterprise ||
            startSceneType == CameraSceneType.AirShipSteal)
        {
            PauseClouds();
        }
        if (startSceneType == CameraSceneType.AirShipEnterprise)
        {
            _cameraController.IsDragging = false;
        }
        if (_curShipBehaviour != null && targetSceneType == CameraSceneType.AirShipEnterprise &&
            _airshipController.CurAirshipType != AirshipType.OtherRewardAirship)
        {
            if (_airshipController.CurAirshipType == AirshipType.OwnRewardAirship)
            {
                AirShipNPCBehaviour.NpcMove(_curShipBehaviour.LevelNodes);
            }
        }
        if (startSceneType == CameraSceneType.AirShipEnterprise)
        {
            RewardBehaviour.HideGridHead();
        }
        switch (targetSceneType)
        {
            case CameraSceneType.AirShipEnterprise:
                {
                    if (!_passLevelController.IsPassingLevel)
                    {
                        _cameraController.MainCamera.SetTempParentNode(EnterpriseFreeMoveCameraParentNode).Forget();
                        _airshipController.CurCameraStatus = CameraStatus.FreeMoveCamera;
                    }
                    await UniTask.Delay(500);
                    ShowChangeSkinEffect();
                    if (_guideController.IsCanGuideGetBagReward)
                    {
                        _guideController.CurBuildGridObj = AirShipBagBehaviour.gameObject;
                        _guideController.GetBagRewardFlow();
                    }
                    if (!_passLevelController.IsPassingLevel && _airshipController.CurAirshipType == AirshipType.OwnRewardAirship)
                    {
                        RewardBehaviour.CheckRemotaReward();
                    }
                }
                break;
            case CameraSceneType.BuildingsNormal:
                _guideController.CheckGuide();
                _guideController.GuideInBuildScene();
                break;
            case CameraSceneType.Slot:
                _guideController.GuideInSlotScene();
                break;
            default:
                break;
        }
    }
    #endregion

    #region 银行引导镜头
    private async void MoveBankGuidCamAsync()
    {
        await UniTask.Delay(500);
        SaveCameraData();
        _airshipController.CurCameraStatus = CameraStatus.RewardMoveCamera;
        PassLevelCameraParentNode.transform.DORotateQuaternion(_curShipBehaviour.BankGuideCamera.rotation, 1f).ToUniTask().Forget();
        await PassLevelCameraParentNode.DOMove(_curShipBehaviour.BankGuideCamera.transform.position, 1f);

        _guideController.ChangeGuideStep<Ship1GuideStep4State>();
    }

    private async void OnResumeBankGuidCam()
    {
        await PassLevNodeMoveNear();
        _airshipController.CurCameraStatus = CameraStatus.FreeMoveCamera;
        _guideController.ChangeGuideStep<Ship1GuideStep5State>();
    }

    private async UniTask PassLevNodeMoveFar()
    {
        PassLevelCameraParentNode.transform.DORotateQuaternion(AirshipConfigBehaviour.FarCameraNode.rotation, 1f).ToUniTask().Forget();
        await PassLevelCameraParentNode.DOMove(AirshipConfigBehaviour.FarCameraNode.transform.position, 1f);
    }

    private async UniTask PassLevNodeMoveNear()
    {
        PassLevelCameraParentNode.transform.DORotateQuaternion(EnterpriseFreeMoveCameraParentNode.rotation, 1f).ToUniTask().Forget();
        await PassLevelCameraParentNode.DOMove(EnterpriseFreeMoveCameraParentNode.transform.position, 1f);
    }

    //镜头拉远的同时打开地图
    private async void OpenShipMapView()
    {
        _ = PassLevNodeMoveFar();
        UIManager.Instance.Open(UIType.ShipMapView);
        await UniTask.Delay(500);
        //打开地图后换船，换成当前建造的船模型  
        _airshipController.CurShowShipID = _airshipController.RewardShipID + 1;
        await RefreshPassLevelShip(_passLevelAniLevel + 1, true);
        AirShipNPCBehaviour.ReSetAirPlane();
        PassLevelCameraParentNode.transform.rotation = EnterpriseFreeMoveCameraParentNode.rotation;
        PassLevelCameraParentNode.transform.position = EnterpriseFreeMoveCameraParentNode.transform.position;
    }

    #endregion

    #region  Skin
    private Transform _curSkinBG;
    private Dictionary<int, AirshipBehaviour> _skinShipsDic = new Dictionary<int, AirshipBehaviour>();//皮肤-船身
    private Dictionary<int, Transform> _skinBG = new Dictionary<int, Transform>();//皮肤对应的主题背景

    private bool IsNeedShowChangeSkinEffect()
    {
        if (_passLevelController.IsPassingLevel)
        {
            return false;
        }
        if (_airshipController.CurAirshipType != AirshipType.OwnRewardAirship)
        {
            return false;
        }
        int.TryParse(SaveUtils.LoadRemoteUser(StorageKey.Airship.ShowSkinAnim, "0"), out int skin);
        int.TryParse(SaveUtils.LoadRemoteUser(StorageKey.Airship.ShowDoorAnim, "0"), out int door);
        int.TryParse(SaveUtils.LoadRemoteUser(StorageKey.Airship.ShowBankAnim, "0"), out int bank);

        var shipid = _airshipController.GetSkinIdByPosition(CosmeticPosition.ShipCosmetic);
        var doorid = _airshipController.GetSkinIdByPosition(CosmeticPosition.ShipGateCosmetic);
        var bankid = _airshipController.GetSkinIdByPosition(CosmeticPosition.ShipBankCosmetic);
        if ((shipid > 1 && skin != shipid) ||
         (doorid > 1 && door != doorid) ||
         (bankid > 1 && bank != bankid))
        {
            return true;
        }
        return false;
    }
    //换船动画
    private async void ShowChangeSkinEffect()
    {
        if (!IsNeedShowChangeSkinEffect())
        {
            return;
        }
        SaveSkinInfo();
        await RefreshRewardShip();
        UIManager.Instance.Open(UIType.ChangeShipEffectView);
        _curShipBehaviour.ShowSkinEffect();
    }

    private void SaveSkinInfo()
    {
        SaveUtils.SaveRemoteUser(StorageKey.Airship.ShowSkinAnim, _airshipController.GetSkinIdByPosition(CosmeticPosition.ShipCosmetic).ToString());
        SaveUtils.SaveRemoteUser(StorageKey.Airship.ShowDoorAnim, _airshipController.GetSkinIdByPosition(CosmeticPosition.ShipGateCosmetic).ToString());
        SaveUtils.SaveRemoteUser(StorageKey.Airship.ShowBankAnim, _airshipController.GetSkinIdByPosition(CosmeticPosition.ShipBankCosmetic).ToString());
    }

    //检查背景主题
    private async void SetSkinBackGround(int index)
    {
        PauseClouds();
        if (_skinBG.ContainsKey(index))
        {
            _curSkinBG = _skinBG[index];
        }
        else
        {
            if (index > AirshipController.MaxShipIndex)
            {
                var res = _airshipConfig.FindAirShipResourceBySkinId(index);
                var resName = res.Split("_");
                await LoadShip<Transform>(index, $"{resName[0]}_{PathConst.AirShip.Bg}{index}", transform.GetChild(0), _skinBG);
            }
            else
            {
                await LoadShip<Transform>(index, $"Ship{index}_{PathConst.AirShip.Bg}{index}", transform.GetChild(0), _skinBG);
            }
            if (_skinBG.ContainsKey(index))
                _curSkinBG = _skinBG[index];
            else
                _curSkinBG = AirShipCloud.transform;
        }
        ResumeClouds();
    }

    private void ResumeClouds()
    {
        _curSkinBG.gameObject.SetActive(true);
        var cloudBehaviour = _curSkinBG.GetComponent<AirShipCloudBehaviour>();
        if (cloudBehaviour != null)
            cloudBehaviour.ResumeClouds();
    }

    private void PauseClouds()
    {
        if (AirShipCloud != null)
        {
            AirShipCloud.gameObject.SetActive(false);
            AirShipCloud.PauseClouds();
        }
        if (_curSkinBG == null) return;
        var cloudBehaviour = _curSkinBG.GetComponent<AirShipCloudBehaviour>();
        if (cloudBehaviour != null)
            cloudBehaviour.PauseClouds();
        _curSkinBG.gameObject.SetActive(false);
    }

    #endregion

    #region 背包 

    private void SetBagNode(Transform bagNode)
    {
        AirShipBagBehaviour.transform.SetParent(bagNode);
        AirShipBagBehaviour.transform.localPosition = Vector3.zero;
        AirShipBagBehaviour.transform.localRotation = Quaternion.identity;
        AirShipBagBehaviour.transform.localScale = Vector3.one * 1.3f;
        var b = _airshipController.CurShowShipID == _airshipController.RewardShipID &&
                  _airshipController.CurAirshipType == AirshipType.OwnRewardAirship &&
                  _userController.Level > AirshipController.ShipCount;
        Log.Info($"[AirShipBagBehaviour].SetBagState:{b}");
        AirShipBagBehaviour.gameObject.SetActive(b);
    }

    #endregion

    //private void OnDrawGizmos()
    //{
    //    Gizmos.color = Color.red;
    //    Gizmos.DrawLine(_airshipConfigBehaviour.NearCameraNode.position, _airshipConfigBehaviour.FarCameraNode.position);
    //    //Gizmos.color = Color.green;
    //    //Gizmos.DrawLine(_origin, _origin + _yAxis * 100000000f);
    //    //Gizmos.color = Color.blue;
    //    //Gizmos.DrawLine(_origin, _origin + _zAxis * 100000000f);
    //}

    //public GameObject testGrid;
    // private void Update()
    // {
    //     if (_cameraController.MainCamera != null && _curShipBehaviour != null)
    //     {
    //         //Log.Info("船尾相对位置:" + (_cameraController.MainCamera.transform.position - _curShipBehaviour.AirshipHull1.transform.position));
    //         // Log.Info("船头相对位置:" + (_cameraController.MainCamera.transform.position - _curShipBehaviour.AirshipProw.transform.position));
    //         //_cameraController.MainCamera.transform.position = _curShipBehaviour.AirshipHull1.transform.position + offset2Grid;
    //         // GetFreeMovePos();
    //         // PassLevelCameraParentNode.transform.rotation = AirshipConfigBehaviour.FarCameraNode.rotation;
    //         // PassLevelCameraParentNode.transform.position = _defaultCenterPoint;
    //         // if (testGrid != null)
    //         // {
    //         //     Log.Info("testGrid.transform.position:" + (_cameraController.MainCamera.transform.position - testGrid.transform.position));
    //         // }
    //     }
    // }
}

