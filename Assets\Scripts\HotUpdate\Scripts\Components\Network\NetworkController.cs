using Best.HTTP.JSON.LitJson;
using Best.HTTP.Shared.PlatformSupport.Memory;
using Best.WebSockets;
using Best.WebSockets.Implementations;
using CrazyCube.Protobuf.Public;
using Google.Protobuf;
using System;
using System.Collections.Generic;
using TKFrame;

/// <summary>
/// 网络通信-业务Controller模块
/// </summary>
public partial class NetworkController
{
    //网络状态码(未连接/连接中/已连接/已认证)
    public const int DisConnected = 0;//未连接
    public const int Connecting = 1;//连接中
    public const int Connected_UnSigned = 2;//已连接，但还未认证
    public const int Connected_Signed = 3;//已连接，已认证

    //网络标签
    private string[] NetworkStateName = new string[] { "DisConnected", "Connecting", "Connected_UnSigned", "Connected_Signed" };

    //网络状态
    public int NetworkState = -1;

    //服务器URL地址
    public string ServerURL { get; set; }

    //会话ID
    public long SessionId { get; set; }

    //异常自动重试次数，超过次数将弹框提示异常
    public int retryTimes_Error { get; set; }

    //异常自动最大重试次数
    public int retryTimes_Error_Max { get; set; }

    //代理服务器地址
    public List<string> ServerProxy = new List<string>();

    //消息回调
    private Dictionary<int, List<Action<int, byte[], int>>> msgHandles = new Dictionary<int, List<Action<int, byte[], int>>>();

    //websocket服务
    private WebSocket webSocket;

    //重连回调
    private Action ReconnectCallback;

    //连接回调
    private Action ConnectCallback;

    //是否已经连接
    public bool IsConnected
    {
        get
        {
            return webSocket != null ? webSocket.IsOpen : false;
        }
    }

    //请求ID, 从10000开始
    public int RequestID = 10000;
}

public partial class NetworkController : TKController
{
    /// <summary>
    /// 初始化
    /// </summary>
    public NetworkController()
    {
        retryTimes_Error_Max = 3;

        SdkWrapper.AddListener(SdkWrapper.EventConst.Net_State_DISCONNECT, Net_State_DISCONNECT);
        SdkWrapper.AddListener(SdkWrapper.EventConst.Net_State_MOBILE_CONNECT, Net_State_MOBILE_CONNECT);
        SdkWrapper.AddListener(SdkWrapper.EventConst.Net_State_WIFI_CONNECT, Net_State_WIFI_CONNECT);
        AddRecvCallback(WebsocketCommand.SessionBind, OnWebsocketSessionBind);
    }

    public override void Dispose()
    {
        RemoveNetStateListen();
        base.Dispose();
    }

    /// <summary>
    /// 移除侦听网络状态
    /// </summary>
    public void RemoveNetStateListen()
    {
        SdkWrapper.RemoveListener(SdkWrapper.EventConst.Net_State_DISCONNECT, Net_State_DISCONNECT);
        SdkWrapper.RemoveListener(SdkWrapper.EventConst.Net_State_MOBILE_CONNECT, Net_State_MOBILE_CONNECT);
        SdkWrapper.RemoveListener(SdkWrapper.EventConst.Net_State_WIFI_CONNECT, Net_State_WIFI_CONNECT);
        RemoveRecvCallback(WebsocketCommand.SessionBind, OnWebsocketSessionBind);
    }

    public void Net_State_DISCONNECT(string state)
    {
        Log.LogFile("NetworkController.Net_State_DISCONNECT");
    }

    public void Net_State_MOBILE_CONNECT(string state)
    {
        Log.LogFile("NetworkController.Net_State_MOBILE_CONNECT");
    }

    public void Net_State_WIFI_CONNECT(string state)
    {
        Log.LogFile("NetworkController.Net_State_WIFI_CONNECT");
    }

    /// <summary>
    /// SessionBind回调
    /// </summary>
    /// <param name="code"></param>
    /// <param name="body"></param>
    /// <param name="requestId"></param>
    private void OnWebsocketSessionBind(int code, byte[] body, int requestId)
    {
        if (code == 0)
        {
            SetNetworkState(Connected_Signed);

            Log.Info($"[NetworkController] OnSessionBind Success");
        }
        else
        {
            Log.Error($"[NetworkController] OnSessionBind Error: {code} - {ControllerManager.Get<UserController>().UID}");
            //绑定失败，2s自动重连
            Timer.SetTimeout(2, AutoStartReconnect);
        }
    }

    /// <summary>
    /// 开始连接网络
    /// </summary>
    public void StartNetworkService()
    {
        ConnectCallback = InternalStartNetworkService;

        Close();
    }

    private void InternalStartNetworkService()
    {
        if (webSocket != null)
        {
            Log.LogFile($"[NetworkController] InternalStartNetworkService - webSocket.State:{webSocket.State} exit");
            return;
        }

        //网络状态-连接中
        SetNetworkState(Connecting);

        //创建消息通信服务
        var url = ServerURL;

        //正式服切换地址
        if (CGameUrl.curServerType == CGameUrl.goLangReleaseServer)
        {
            var proxy = ServerProxy;
            if (retryTimes_Error > 0 && retryTimes_Error <= proxy.Count)
            {
                url = proxy[retryTimes_Error - 1];
            }
        }
        Log.LogFile("[NetworkController] serverURL:" + url + ", retryTimes_Error:" + retryTimes_Error);

        if (webSocket == null)
        {
            webSocket = new WebSocket(new Uri(url));
        }

        SessionId++;
        Log.Info("[NetworkController] SessionId:" + SessionId);

        webSocket.OnOpen += OnOpen;
        webSocket.OnBinary += OnBinary;
        webSocket.OnClosed += OnClosed;

        //ws 建立连接
        webSocket.Open();
        Log.Info("[NetworkController] Websocket Open");
    }

    void OnOpen(WebSocket ws)
    {
        Log.LogFile("[NetworkController] OnOpen");
        //网络状态-已连接(未认证)
        SetNetworkState(Connected_UnSigned);

        //网络状态-已连接(未认证)
        SendMsg(WebsocketCommand.SessionBind, ByteString.Empty);

        //心跳
        ControllerManager.Get<HeartbeatController>().Start();
    }

    void OnBinary(WebSocket ws, BufferSegment data)
    {
        try
        {
            int byteCount = data.Count;
            var buffer = BufferPool.Get(byteCount, false);
            Buffer.BlockCopy(data.Data, (int)data.Offset, buffer, 0, byteCount);

            var msg = WebsocketMessage.Parser.ParseFrom(buffer);
            int cmd = (int)msg.Cmd;
            Log.LogFile($"[Socket Recive] {cmd}:: {msg}");
            int code = (int)msg.Code;
            int requestId = (int)msg.RequestId;

            //更新心跳时间
            ControllerManager.Get<HeartbeatController>().SyncLastReceiveMsgTime(msg.Timestamp);

            if (code == 0)
            {
                var body = msg.Body.ToByteArray();
                if (msgHandles.TryGetValue(cmd, out List<Action<int, byte[], int>> list))
                {
                    foreach (var callback in list)
                    {
                        callback?.Invoke(code, body, requestId);
                    }
                }
            }
            else
            {
                if (msgHandles.TryGetValue(cmd, out List<Action<int, byte[], int>> list))
                {
                    foreach (var callback in list)
                    {
                        callback?.Invoke(code, null, requestId);
                    }
                }
            }
            BufferPool.Release(buffer);
        }
        catch (Exception e)
        {
            Log.Error("[NetworkController] OnBinary Exception:" + e.Message);
        }
    }

    void OnClosed(WebSocket ws, WebSocketStatusCodes code, string reason)
    {
        Log.Info($"[NetworkController] OnClosed {code}-{reason}");
        if (webSocket != null)
        {
            webSocket = null;
            Log.Info("[NetworkController] OnClosed");

            //网络状态-未连接
            SetNetworkState(DisConnected);

            //连接回调
            ConnectCallback?.Invoke();
            ConnectCallback = null;
        }
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="cmd"></param>
    /// <param name="buffer"></param>
    /// <param name="socketDisposedCallback">网络问题回调</param>
    public void SendMsg(WebsocketCommand cmd, ByteString buffer, Action socketDisposedCallback = null)
    {
        if (webSocket != null && webSocket.IsOpen)
        {
            var userController = ControllerManager.Get<UserController>();
            WebsocketForwardRequest msg = new WebsocketForwardRequest();
            msg.Cmd = cmd;
            msg.Sid = userController.SID;
            msg.Uid = userController.UID;
            msg.Pid = userController.PID;
            msg.RequestId = RequestID++;
            msg.Body = buffer;
            webSocket.Send(msg.ToByteArray());
            Log.Info($"[Socket Send] {msg}");
        }
        else
        {
            Log.LogFile($"[Socket Send] {cmd} NetworkService is Disposed");
            socketDisposedCallback?.Invoke();
        }
    }

    /// <summary>
    /// 关闭网络
    /// </summary>
    public void Close()
    {
        //关闭心跳
        ControllerManager.Get<HeartbeatController>().Close();

        if (webSocket != null)//防止多次调用
        {
            Log.Info($"[NetworkController] Websocket Close - webSocket.State:{webSocket.State}");
            webSocket.Close();
        }
        else
        {
            Log.Info("[NetworkController] Websocket Close ConnectCallback");
            ConnectCallback?.Invoke();
            ConnectCallback = null;
        }
    }

    /// <summary>
    /// 自动开始重连
    /// </summary>
    public void AutoStartReconnect()
    {
        Log.LogFile($"[NetworkController] AutoStartReconnect retryTimes {retryTimes_Error}");
        if (webSocket != null)
        {
            Log.LogFile($"[NetworkController] AutoStartReconnect - webSocket.State:{webSocket.State}");
            ConnectCallback = InternalAutoStartReconnect;
            Close();
        }
        else
        {
            Log.Info("[NetworkController] AutoStartReconnect websocket is null");
            InternalAutoStartReconnect();
        }
    }

    private void InternalAutoStartReconnect()
    {
        retryTimes_Error++;
        if (retryTimes_Error > retryTimes_Error_Max)
        {
            retryTimes_Error = 0;
        }

        Log.Info($"[NetworkController] InternalAutoStartReconnect retryTimes {retryTimes_Error}");
        ReconnectCallback = OnReconnect;
        Timer.SetTimeout(1, () => { InternalStartNetworkService(); });
    }

    private void OnReconnect()
    {
        Log.Info("[NetworkController] OnReconnect");
        ControllerManager.Get<TaskController>().Done(WaitingTaskType.WaitingCircle);
        EventCenter.Instance.Event.TriggerEvent(EventType.Network.Reconnect);
    }

    public void AddRecvCallback(WebsocketCommand cmd, Action<int, byte[], int> callbcak)
    {
        AddRecvCallback((int)cmd, callbcak);
    }

    /// <summary>
    /// 添加消息响应回调函数
    /// </summary>
    public void AddRecvCallback(int id, Action<int, byte[], int> callbcak)
    {
        if (msgHandles.TryGetValue(id, out List<Action<int, byte[], int>> list))
        {
            list.Add(callbcak);
        }
        else
        {
            msgHandles.Add(id, new List<Action<int, byte[], int>> { callbcak });
        }
    }

    /// <summary>
    /// 删除消息响应回调函数
    /// </summary>
    /// <param name="id"></param>
    /// <param name="callbcak"></param>
    public void RemoveRecvCallback(int id, Action<int, byte[], int> callbcak)
    {
        if (msgHandles.TryGetValue(id, out List<Action<int, byte[], int>> list))
        {
            list.Remove(callbcak);
        }
    }

    public void RemoveRecvCallback(WebsocketCommand cmd, Action<int, byte[], int> callbcak)
    {
        RemoveRecvCallback((int)cmd, callbcak);
    }

    /// <summary>
    /// 设置网络状态
    /// </summary>
    public void SetNetworkState(int state)
    {
        Log.Info($"[NetworkController] SetNetworkState {NetworkStateName[state]}");
        NetworkState = state;
        if (state == Connected_Signed)
        {
            retryTimes_Error = 0;//登录成功，清理重试次数

            //断线重连，业务重连监听重连事件
            ReconnectCallback?.Invoke();
        }
    }

    /// <summary>
    /// 设置服务器URL地址
    /// </summary>
    /// <param name="ServerURL"></param>
    public void SetServerURL(string url, JsonData proxy)
    {
        ServerURL = url;

        if (proxy != null)
        {
            ServerProxy.Clear();
            foreach (var obj in proxy)
            {
                ServerProxy.Add(obj.ToString());
            }
        }
    }
}