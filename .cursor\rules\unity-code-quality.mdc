---
description: 
globs: 
alwaysApply: true
---
# Unity 代码质量与构建规范

## 代码质量
- **异常处理**:
  - **通用捕获**: 文件I/O、异步操作等推荐使用 `try-catch` 进行异常捕获，特别是 `OperationCanceledException`。
  - **业务重试**: 对网络请求等关键业务失败场景，实现了特定的重试逻辑（如 `DialogUtils.ShowLoginRetryDialog`），应优先使用。
- **资源清理**: 在 `OnDestroy` 中必须清理事件监听、对象池实例、定时器等，防止内存泄漏。
- **命名规范**: https://www.yooasset.com/docs/community/CodeStyle
- **注释规范**: 对重要方法和复杂逻辑需要添加清晰的注释或XML文档注释。

## 构建与部署
- **程序集定义**: 使用 `.asmdef` 文件管理程序集依赖，以加快编译速度。
- **条件编译**: 使用 `#if` 预处理指令区分不同游戏版本（`GAME_DICE`, `GAME_SLOT`）和不同构建配置。
- **热更新支持**: `HotUpdate` 模块支持运行时热更新，无需重新安装应用。
- **版本管理**: 通过Build配置管理不同环境的构建参数。

---
详细实现见相关文件。



