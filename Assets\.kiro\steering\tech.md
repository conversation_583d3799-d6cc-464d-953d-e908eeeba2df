# 技术栈详解

## 核心框架

### 游戏引擎
- **Unity 2022.3+ LTS** - 主要游戏引擎
- **C#** - 主要编程语言
- **HybridCLR** - 热更新框架，支持运行时代码更新
- **YooAsset** - 资源包管理和热更新系统

### 热更新架构
- **GameFrame** - 核心框架，不参与热更新
- **HotUpdate** - 热更新代码，包含所有游戏逻辑
- **资源热更新** - 通过YooAsset实现资源的增量更新
- **代码热更新** - 通过HybridCLR实现C#代码的运行时更新

## 架构模式

### 设计模式
- **MVC模式** - Model、View、Controller分离
- **组件化架构** - TKController、TKModel基类系统
- **单例模式** - ControllerManager进行服务管理
- **事件驱动架构** - Model通知和UI更新机制
- **观察者模式** - 数据变化通知系统

### 核心基类
```csharp
// 控制器基类
public class TKController : MonoBehaviour
{
    // 服务生命周期管理
    // 依赖注入支持
}

// 数据模型基类
public class TKModel
{
    // 数据变化通知
    // 持久化支持
}

// UI视图基类
public class UIViewBase : MonoBehaviour
{
    // UI生命周期管理
    // 事件绑定机制
}
```

## 关键库和SDK

### 广告和货币化
- **AppLovin MAX SDK** - 广告中介平台
  - 激励视频广告
  - 横幅广告
  - 插屏广告
  - 广告收入追踪

### 分析和追踪
- **Firebase** - 分析和崩溃报告
  - 用户行为分析
  - 崩溃监控
  - 远程配置
- **Facebook SDK** - 社交功能和分析
  - 用户登录
  - 分享功能
  - 事件追踪
- **Adjust SDK** - 归因和分析
  - 安装归因
  - 事件追踪
  - 收入追踪

### UI和动画
- **DOTween** - 动画框架
  - UI动画
  - 缓动函数
  - 序列动画
- **TextMeshPro** - 高级文本渲染
  - 多语言支持
  - 富文本格式
  - 字体渲染优化
- **Spine** - 2D骨骼动画
  - 角色动画
  - UI动画效果

### 网络和异步
- **UniTask** - 异步/等待操作
  - 替代Unity Coroutine
  - 更好的性能
  - 异常处理
- **Best HTTP** - 网络通信
  - HTTP请求
  - WebSocket支持
  - JSON处理

### 其他工具
- **Easy Save 3** - 数据持久化
- **DOTween** - 补间动画
- **LoopScrollRect** - 循环滚动列表优化

## 构建系统

### Unity构建
- **Unity Cloud Build** 或本地构建
- **批处理模式构建** 支持自动化
- **多平台构建配置**

### iOS构建流程
```bash
# Unity导出后，构建Xcode项目
xcodebuild -workspace Unity-iPhone.xcworkspace -scheme Unity-iPhone -configuration Release

# 自动上传符号表 (通过包含的Python脚本)
python3 UploadSymbols.py

# 自动构建脚本
python3 AutoBuildXcocde.py
```

### Android构建
- **Gradle** 构建系统
- **多渠道包配置**
- **签名和混淆配置**

### 资源包管理
- 使用YooAsset收集器设置
- 配置自定义地址规则进行资源命名
- 通过YooAsset构建管道构建资源包

## 常用命令

### Unity编辑器操作
```bash
# 从项目根目录在Unity中打开项目
Unity.exe -projectPath .

# iOS构建 (通过Unity批处理模式)
Unity.exe -batchmode -quit -projectPath . -buildTarget iOS -executeMethod BuildScript.BuildiOS

# Android构建
Unity.exe -batchmode -quit -projectPath . -buildTarget Android -executeMethod BuildScript.BuildAndroid

# 资源包构建
Unity.exe -batchmode -quit -projectPath . -executeMethod YooAsset.Editor.AssetBundleBuilderHelper.BuildAssetBundles
```

### 开发环境配置
- **Unity 2022.3+ LTS** 必需
- **Xcode 14+** iOS开发
- **Android Studio** Android开发
- **Visual Studio** 或 **JetBrains Rider** C#开发

### 版本控制
- **Git** 版本控制
- **Git LFS** 大文件存储
- **分支策略** - develop/release/hotfix

## 性能优化

### 内存管理
- 对象池模式
- 资源预加载和卸载
- GC优化策略

### 渲染优化
- UI批处理
- 纹理压缩
- Shader优化

### 网络优化
- 请求缓存
- 断线重连
- 数据压缩