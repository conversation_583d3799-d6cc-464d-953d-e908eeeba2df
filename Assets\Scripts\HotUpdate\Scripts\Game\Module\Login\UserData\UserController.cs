﻿using CrazyCube.Protobuf.CityService;
using CrazyCube.Protobuf.Public;
using CrazyCube.Protobuf.UserService;
using Cysharp.Threading.Tasks;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.WellKnownTypes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using TKFrame;
using UnityEngine;

#if GAME_SLOT
public partial class UserController //Fields
{
    public long FbBindRewardNum = 80;

    //金币数量
    public long Coins
    {
        get
        {
            return ResponseData.UserAsset.Coin;
        }
        set
        {
            ResponseData.UserAsset.Coin = value;
            EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateCoins, value);
        }
    }

    private void _Dispose()
    {
        
    }
}
#endif

#if GAME_DICE
public partial class UserController //Fields
{
    public long FbBindRewardNum = 50;
    public Action<long> OnUpdateCoins;

    private bool _isSyncing = false; //是否正在同步
    private float _syncAmount = 0; //缓存要同步的本地金币
    private CancellationTokenSource _timeoutCts; //取消令牌
    
    /// <summary>
    /// 金币同步时间戳
    /// </summary>
    public long SyncCoinsTs = 0;
    
    /// <summary>
    /// 用户金币，不包含本地累加金币
    /// </summary>
    public long UserCoin
    {
        get
        {
            return ResponseData.UserAsset.Coin;
        }
    }
    
    //金币数量
    public long Coins
    {
        get
        {
            return ResponseData.UserAsset.Coin + (long)_localCoins;
        }
        set
        {
            ResponseData.UserAsset.Coin = value;
            // 仅当非同步状态时更新UI
            if (!_isSyncing)
            {
                var localSum = ResponseData.UserAsset.Coin + (long)_localCoins;
                EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateCoins, localSum);
                //Log.Info($"OnUpdateCoins [Coins] 开始同步金币, 当前金币数：{localSum} ");
                OnUpdateCoins?.Invoke(localSum);
            }
        }
    }

    private List<float> trackCoins = new List<float>();
    private float _localTrack = 0;

    private float _localCoins = 0;
    // 本地金币数量
    public float LocalCoins
    {
        get
        {
            return _localCoins;
        }
        set
        {
            if (value > 0) // 增加金币
            {
                _localTrack += value;
            }
            _localCoins = value;
            // 仅当非同步状态时更新UI
            if (!_isSyncing)
            {
                EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateCoins, Coins);
                //Log.Info($"OnUpdateCoins [LocalCoins] 开始同步金币, UserAsset.Coin: {ResponseData.UserAsset.Coin} _localCoins: {_localCoins}");
                OnUpdateCoins?.Invoke(Coins);
            }
        }
    }

    public void SyncLocalCoins()
    {
        if (_localCoins != 0)
        {
            // 取消之前的超时检测
            _timeoutCts?.Cancel();
            _timeoutCts?.Dispose();
            _timeoutCts = new CancellationTokenSource();
            // 开始同步
            _isSyncing = true;
            //Log.Info($"OnUpdateCoins 开始同步金币, 当前金币数：{Coins} 本地金币数：{_localCoins}");

            // 保存当前值
            _syncAmount = _localCoins;
            _localCoins = 0;

            var request = new SyncCoinRequest();
            request.Uid = this.UID;
            request.Num = (long)_syncAmount;
            NetworkController networkController = ControllerManager.Get<NetworkController>();
            networkController.SendMsg(WebsocketCommand.SyncCoin, ByteString.CopyFrom(request.ToByteArray()), OnSocketDisposed);

            if (Coins < 0)
            {
                Log.Error("金币消费，超过拥有的金币");
                // 容错，直接把本地消耗丢掉
                _localCoins = 0;
            }

            trackCoins.Add(_localTrack);
            if (trackCoins.Count >= 30)
            {
                var id = ControllerManager.Get<UserController>().SquareId.ToString();
                TrackUtil.OperateOnlineReward(id, trackCoins, trackCoins.Sum());
                trackCoins.Clear();
            }
            _localTrack = 0;

            // 启动UniTask超时检测
            CheckSyncTimeout(_timeoutCts.Token).Forget();
        }
    }

    public void OnSyncCoinCommand(int code, byte[] resp, int requestId)
    {
        if (!_isSyncing) return;
        // 取消超时检测
        _timeoutCts?.Cancel();
        // 结束同步状态
        if (code == 0)
        {
            var msg = ProtobufUtil.Parser<SyncCoinResponse>(resp, () => new SyncCoinResponse());
            Log.Info($"localCoins:{_localCoins}  SyncCoinResponse Msg: {msg}");
            if (resp != null && msg.Result.Count > 0 && msg.Result[0].PropsId == PropID.Coin)
            {
                var data = msg.Result[0];
                Coins = data.CurNum;
                SyncCoinsTs = data.CurTs;
                //Log.Info($"OnUpdateCoins 同步金币成功, 服务器返回金币数：{msg.Result[0].CurNum} 当前金币数Coins：{Coins} 本地金币数localCoins：{_localCoins}");
            }
            else
            {
                _localCoins += _syncAmount; //同步失败恢复本地金币
                Log.Warning($"OnUpdateCoins 同步金币失败, Code: {code}");
            }
        }
        else
        {
            _localCoins += _syncAmount; //同步失败恢复本地金币
            Log.Warning($"OnUpdateCoins 同步金币失败, 恢复本地金币数：{_localCoins} Code: {code}");
        }
        _isSyncing = false;
    }

    // 超时检测
    private async UniTaskVoid CheckSyncTimeout(CancellationToken ct)
    {
        try
        {
            await UniTask.Delay(1000, cancellationToken: ct);

            if (_isSyncing)
            {
                _localCoins += _syncAmount; //同步失败恢复本地金币
                _isSyncing = false;

                EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateCoins, Coins);
                OnUpdateCoins?.Invoke(Coins);
                Log.Warning("金币同步超时，恢复本地更新");
            }
        }
        catch (OperationCanceledException)
        {

        }
    }

    // 在Dispose中清理资源
    private void _Dispose()
    {
        // 清理超时检测
        _timeoutCts?.Cancel();
        _timeoutCts?.Dispose();
        _timeoutCts = null;
    }

    /// <summary>
    /// 网络连接断开处理
    /// 当网络连接出现问题时自动重连
    /// </summary>
    private void OnSocketDisposed()
    {
        Log.Info("建造 网络问题，重连");
        ControllerManager.Get<NetworkController>().AutoStartReconnect();
    }
}
#endif


public partial class UserController //Fields
{
    public string LoginType = Const.LOGIN_TYPE.GUEST;

    public LoginResponse ResponseData; //登陆回包数据

    public void UpdateUserInfo(UserInfo info)
    {
        ResponseData.UserInfo = info;
    }
}

public partial class UserController //LT Properties
{
    /// <summary>
    /// 广场ID
    /// </summary>
    public long SquareId;
}

public partial class UserController //Properties
{
    //SID
    public long SID
    {
        get
        {
            return ResponseData.UserInfo.Sid;
        }
    }

    //PID
    public long PID
    {
        get
        {
            return ResponseData.UserInfo.Pid;
        }
    }

    //UID
    public long UID
    {
        get
        {
            if (ResponseData != null)
            {
                return ResponseData.Uid;
            }
            else
            {
                Log.Warning("UID is Null");
                return -1;
            }
        }
    }

    //体力数量
    public long Spins
    {
        get
        {
            return ResponseData.UserAsset.Spin;
        }
        set
        {
            Log.Info($"Spins SetValue: {value}");
            ResponseData.UserAsset.Spin = value;
            EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateSpins, value);
        }
    }

    //星星数量
    public long Stars
    {
        get
        {
            return ResponseData.UserAsset.Star;
        }
        set
        {
            ResponseData.UserAsset.Star = value;
            EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateStars, value);
        }
    }

    //盾牌数量
    public long Shield
    {
        get
        {
            return (long)ResponseData.UserAsset.Shield;
        }
        set
        {
            ResponseData.UserAsset.Shield = value;
            EventCenter.Instance.Event.TriggerEvent<long>(EventType.UserData.UpdateShield, value);
        }
    }

    //关卡等级
    public int LevelId
    {
        get
        {
            return (int)ResponseData.UserAsset.Level;
        }
        set
        {
            ResponseData.UserAsset.Level = value;
            EventCenter.Instance.Event.TriggerEvent<int>(EventType.UserAsset.Level, value);
        }
    }

    public int Level
    {
        get
        {
            return LevelId;
        }
    }

    //名称
    public string Nick
    {
        get
        {
            return ResponseData.UserInfo.Name;
        }
        set
        {
            ResponseData.UserInfo.Name = value;
            EventCenter.Instance.Event.TriggerEvent(EventType.UserData.UpdateName);
        }
    }

    //性别
    public string Gender
    {
        get
        {
            return ResponseData.UserInfo.Gender;
        }
    }

    //头像
    public string Avatar
    {
        get
        {
            return ResponseData.UserInfo.Avatar;
        }
        set
        {
            ResponseData.UserInfo.Avatar = value;
            EventCenter.Instance.Event.TriggerEvent(EventType.UserData.UpdateAvatar);
        }
    }

    //头像框
    public long AvatarFrame
    {
        get
        {
            return ResponseData.UserInfo.AvatarFrame;
        }
        set
        {
            ResponseData.UserInfo.AvatarFrame = value;
            EventCenter.Instance.Event.TriggerEvent(EventType.UserData.UpdateAvatarFrame);
        }
    }

    //签名
    public string Signature
    {
        get
        {
            return ResponseData.UserInfo.Signature;
        }
    }

    //国家
    public string Country
    {
        get
        {
            return ResponseData.UserInfo.Country;
        }
    }

    //邮件
    public string Email
    {
        get
        {
            return ResponseData.UserInfo.Email;
        }
    }

    //默认格子分组id
    public long ShipConfigId
    {
        get
        {
            return ResponseData.UserNoviceGuide.ShipConfigId;
        }
    }

    //称号
    public long TitleId
    {
        get
        {
            return ResponseData.UserInfo.TitleId;
        }
    }

    //绑定fb所获得的奖励（前端写死）
    public RepeatedField<PrizeShow> BindFbReward
    {
        get
        {
            RepeatedField<PrizeShow> curRewards = new RepeatedField<PrizeShow>();
            PrizeShow coinPrizeShow = new PrizeShow();
            coinPrizeShow.PropsId = PropID.Spin;
            coinPrizeShow.Num = FbBindRewardNum;
            curRewards.Add(coinPrizeShow);
            return curRewards;
        }
    }

    //注册时间
    public long CreateTs
    {
        get
        {
            return ResponseData.UserInfo.CreateTs;
        }
    }

    public string GetBindFbRewardNum()
    {
        return BindFbReward[0].GetNum().ToString();
    }
}

public partial class UserController //OnWebsocktMessage&HttpMessage
{
    public void OnUserAssetCommand(int code, byte[] resp, int requestId)
    {
        if (code == 0)
        {
            var msg = ProtobufUtil.Parser<UserAsset>(resp, () => new UserAsset());
            Log.Info($"UserAssetCommand Msg: {msg}");

            if (resp != null)
            {
                Spins = msg.Spin;
                Stars = msg.Star;
                Coins = msg.Coin;
                Shield = msg.Shield;
                LevelId = (int)msg.Level;
                ControllerManager.Get<SystemUnlockController>().TempLevel = (int)msg.Level;
            }
        }
    }

    public void OnReconnect()
    {
        ControllerManager.Get<TaskController>().AddTask(new UserAssetSyncTask(), isFirst: true);
    }

    /// <summary>
    /// 删除账号, Debug模式下使用
    /// </summary>
    public void RequestDeleteAccont(Action<byte[]> successCallback, Action failCallback)
    {
        //发送建造请求
        DeleteAccountRequest deleteAccountRequest = new DeleteAccountRequest();
        deleteAccountRequest.Uid = this.UID;
        deleteAccountRequest.Source = "前端";
        Log.Info($"[UserController] RequestDeleteAccont url:{HttpServiceRoute.DeleteAccountRequestUrl} deleteAccountRequest:{deleteAccountRequest.ToString()}");
        GatewayHttpService.Post(HttpServiceRoute.DeleteAccountRequestUrl, deleteAccountRequest.ToByteArray(), (Action<bool, byte[]>)((status, resp) =>
        {
            if (status)
            {
                DeleteAccountResponse deleteAccountResponse = ProtobufUtil.Parser<DeleteAccountResponse>(resp, () => new DeleteAccountResponse());
                Log.Info($"[UserController] RequestDeleteAccont url:{HttpServiceRoute.DeleteAccountRequestUrl} deleteAccountResponse:{deleteAccountResponse.ToString()}");

                successCallback?.Invoke(resp);
            }
            else
            {
                var response = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"{HttpServiceRoute.DeleteAccountRequestUrl}: {response}");
                failCallback?.Invoke();
            }
        }), () => failCallback?.Invoke());
    }

    /// <summary>
    /// 删除账号
    /// </summary>
    public void ClearAccountCodeRequest()
    {
        var request = new GetClearAccountCodeRequest();
        request.Uid = this.UID;
        GatewayHttpService.Post(HttpServiceRoute.ClearAccountCodeUrl, request.ToByteArray(), (Action<bool, byte[]>)((status, resp) =>
        {
            if (status)
            {
                GetClearAccountCodeResponse response = ProtobufUtil.Parser<GetClearAccountCodeResponse>(resp, () => new GetClearAccountCodeResponse());

                Log.Info($"{HttpServiceRoute.ClearAccountCodeUrl}: {response}");
                var config = ModelManager.Get<GameBasicConfig>();
                if (!string.IsNullOrEmpty(response.Uuid) && !string.IsNullOrEmpty(config.UnregisterUrl))
                {
                    ControllerManager.Get<UrlWrapper>().OpenURL($"{config.UnregisterUrl}?code={response.Uuid}");
                }
            }
        }), null);
    }
}

public partial class UserController : TKController //Methods
{
    public void Initialize()
    {
        NetworkController networkController = ControllerManager.Get<NetworkController>();
        networkController.AddRecvCallback(WebsocketCommand.UserAssetCommand, OnUserAssetCommand);
#if GAME_DICE
        networkController.AddRecvCallback(WebsocketCommand.SyncCoin, OnSyncCoinCommand);
#endif
        EventCenter.Instance.Event.AddListener(EventType.Network.Reconnect, OnReconnect);
    }

    public override void Dispose()
    {
        NetworkController networkController = ControllerManager.Get<NetworkController>();
        networkController.RemoveRecvCallback(WebsocketCommand.UserAssetCommand, OnUserAssetCommand);
        base.Dispose();
        _Dispose();
    }

    /// <summary>
    /// 是否在注册之后的一段时间内
    /// </summary>
    /// <returns>分钟</returns>
    public bool IsInTimeByCreatTime(int min)
    {
        double tm = TimeTool.CalculateSecond(CreateTs, TimeTool.ServerTime());
        if (tm < min * 60)
        {
            return true;
        }
        return false;
    }
}
