# 上报失败导致收集活动数据对不上的问题分析

## 问题根源

### 当前流程中的问题

1. **RollOnAnim() 中的处理顺序**：
```csharp
private void RollOnAnim()
{
    // 1. 获取投掷结果
    DiceFaceArrayToPlay = GetCurDiceRusult(); // 这里已经将结果移到WaitingSubmitSequence
    
    // 2. 处理奖励类型（包括收集数据）
    HandleRewardType(DiceFaceArrayToPlay); // 创建ActScoreData
    
    // 3. 触发网络投掷事件
    EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);
    
    // 4. 上报投掷结果
    SubmitRollResult(); // 可能失败
}
```

2. **收集任务的触发时机**：
```csharp
// 在 DiceMainRewardLayerBehaviour.cs 中
if (hasSlotCollect)
{
    _diceController.AddCollectTask(result.Result, result.ActData); // 立即添加收集任务
    await UniTask.Delay(50);
}
```

3. **收集任务的执行**：
```csharp
// 在 SlotTaskCollect.cs 中
public void Execute()
{
    // 直接触发收集事件，更新本地UI
    EventCenter.Instance.Event.TriggerEvent<ActScoreData>(EventType.SLOT.ResultCollect, scoreData);
}
```

4. **上报失败的处理**：
```csharp
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    if (code != 0) // 上报失败
    {
        // 只是清空队列，但收集数据已经被本地处理了
        WaitingSubmitSequence.Clear();
        RequestBeforeResults((list) => {
            DicePlayingSequence = list; // 重新获取服务器数据
        }, null);
    }
}
```

## 问题的具体表现

1. **本地收集进度超前**：
   - 用户看到收集进度增加了
   - 但服务器实际没有记录这次收集
   - 下次同步时会发现数据不一致

2. **重复计算收集积分**：
   - 如果上报失败后重新播放相同结果
   - 可能会再次触发收集事件
   - 导致本地积分重复增加

3. **服务器数据丢失**：
   - 上报失败意味着服务器没有收到收集数据
   - 但本地已经消费了这个结果
   - 造成永久性的数据不一致

## 解决方案

### 方案1：延迟收集数据处理（推荐）

将收集数据的处理延迟到上报成功之后：

1. **修改 RollOnAnim() 流程**：
   - 只处理基础奖励（金币、体力等）
   - 收集数据处理移到上报成功回调中

2. **在上报成功回调中处理收集**：
   - 确保服务器已确认收到数据
   - 再触发本地收集事件和UI更新

### 方案2：增加收集数据回滚机制

在上报失败时回滚已处理的收集数据：

1. **记录收集操作**：
   - 在处理收集数据前记录操作
   - 包括增加的积分、阶段变化等

2. **上报失败时回滚**：
   - 撤销本地收集进度更新
   - 恢复到处理前的状态

### 方案3：服务器数据优先

完全依赖服务器数据，本地不做预处理：

1. **移除本地收集计算**：
   - 不在本地计算收集积分
   - 等待服务器返回最终数据

2. **服务器返回完整数据**：
   - 包括更新后的收集进度
   - 本地直接应用服务器数据

## 推荐的具体修改方案

### 修改1：调整 RollOnAnim() 流程

```csharp
private void RollOnAnim()
{
    // 记录上一次金币同步的事件戳
    LastSyncCoinsTs = ControllerManager.Get<UserController>().SyncCoinsTs;

    // 暂停投掷状态机
    PauseRollDice();
    // 消耗体力
    _userController.Spins -= _lastSlotBet;
    // 记录体力消耗
    TrackUtil.Consumespins(_lastSlotBet);
    // 获取当前投掷结果
    DiceFaceArrayToPlay = GetCurDiceRusult();

    // 只处理基础奖励，不处理收集数据
    HandleBasicRewardType(DiceFaceArrayToPlay);

    // 触发网络投掷事件
    EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);

    // 上报投掷结果，成功后处理收集数据
    SubmitRollResult(() => {
        // 上报成功后处理收集数据
        HandleCollectRewardType(DiceFaceArrayToPlay);
    });
}
```

### 修改2：分离基础奖励和收集奖励处理

```csharp
// 新增：只处理基础奖励
private void HandleBasicRewardType(long[] diceFaces)
{
    DiceRollResult.Clear();

    // 将diceFaces中的每个元素添加到Result中
    foreach (var slotId in diceFaces)
    {
        DiceRollResult.Result.Add(slotId);
    }

    // 检查是否所有骰子都是相同面值
    DiceRollResult.IsSameResult = AllDiceType(diceFaces, diceFaces[0]);

    // 只处理基础奖励：金币、体力、盾牌等
    // 不处理收集数据
    if (AllDiceType(diceFaces, (int)SlotId.SpinsIcon))
    {
        DiceRollResult.RewardType = DiceRewardType.Spin;
        DiceRollResult.Spins = SlotBet * 10;
    }
    // ... 其他基础奖励处理
}

// 新增：处理收集奖励
private void HandleCollectRewardType(long[] diceFaces)
{
    // 处理收集物奖励
    long collectNum = GetCollectNum(diceFaces, DiceRollResult.RewardType);
    if (collectNum > 0)
    {
        DiceRollResult.IsAllCollect = AllDiceType(diceFaces, (int)SlotId.CollectIcon);

        // 创建活动分数数据
        ActScoreData actScoreData = new ActScoreData();
        actScoreData.Aid = ControllerManager.Get<CollectController>().GetCurrInfo().Aid;
        actScoreData.ActType = ActType.Collect;
        actScoreData.IncrScore = collectNum * SlotBet;

        DiceRollResult.ActData = new RepeatedField<ActScoreData>();
        DiceRollResult.ActData.Add(actScoreData);
        DiceRollResult.CollectNum = collectNum * SlotBet;

        // 现在可以安全地触发收集任务
        TriggerCollectTasks();
    }
}
```

### 修改3：增加收集任务触发方法

```csharp
// 新增：安全地触发收集任务
private void TriggerCollectTasks()
{
    CollectController cc = ControllerManager.Get<CollectController>();
    var rollResult = DiceRollResult.Result;
    bool hasSlotCollect = cc.IsSlotResuldTriggerCollect(rollResult[0], rollResult[1], rollResult[2]);

    if (hasSlotCollect && _activityContoller.IsOnline(ActType.Collect))
    {
        AddCollectTask(DiceRollResult.Result, DiceRollResult.ActData);
    }
}
```

### 修改4：改进上报失败处理

```csharp
void OnReceiveSubmit(int code, byte[] resp, int requestId)
{
    Timer.ClearTimer(_timesetReconnectOnUp);
    _timesetReconnectOnUp = 0;
    IsUping = false;

    if (code == 0) // 上报成功
    {
        var response = ProtobufUtil.Parser<SubSlotResultResponse>(resp,
            () => new SubSlotResultResponse());

        // 移除已成功上报的结果
        for (int i = 0; i < hasPreUpCount; i++)
        {
            WaitingSubmitSequence.RemoveAt(0);
        }

        // 执行上报完成回调（包括收集数据处理）
        UpAction?.Invoke();
    }
    else // 上报失败
    {
        Log.Info($"批量投掷相关 上报失败，需要重新同步数据");

        // 清空待上报队列
        WaitingSubmitSequence.Clear();

        // 重新获取服务器数据，确保数据一致性
        RequestBeforeResults((list) => {
            DicePlayingSequence = list;

            // 如果有待播放的结果，重新播放
            if (DicePlayingSequence.Count > 0)
            {
                RollOnAnim();
            }
        }, null);
    }
}
```

## 额外的安全措施

### 1. 添加收集数据状态标记

```csharp
public partial class DiceController
{
    // 新增：标记收集数据是否已处理
    private bool _isCollectDataProcessed = false;

    // 新增：待处理的收集数据
    private long[] _pendingCollectData = null;
}
```

### 2. 防重复处理机制

```csharp
private void HandleCollectRewardType(long[] diceFaces)
{
    // 防止重复处理
    if (_isCollectDataProcessed)
    {
        Log.Info("收集数据已处理，跳过重复处理");
        return;
    }

    long collectNum = GetCollectNum(diceFaces, DiceRollResult.RewardType);
    if (collectNum > 0)
    {
        _isCollectDataProcessed = true;
        // ... 处理收集数据
    }
}
```

### 3. 重置状态标记

```csharp
private long[] GetCurDiceRusult()
{
    if (DicePlayingSequence == null || DicePlayingSequence.Count == 0)
    {
        return null;
    }

    var first = DicePlayingSequence[0];
    DicePlayingSequence.RemoveAt(0);
    WaitingSubmitSequence.Add(first);

    // 重置收集数据处理状态
    _isCollectDataProcessed = false;
    _pendingCollectData = first;

    return first;
}
```

## 测试验证方案

### 1. 模拟上报失败场景

```csharp
// 在测试环境中强制上报失败
public void ForceSubmitFailure()
{
    // 模拟网络问题导致上报失败
    IsUping = false;
    OnReceiveSubmit(-1, null, 0); // 模拟失败响应
}
```

### 2. 数据一致性检查

```csharp
public bool VerifyCollectDataConsistency()
{
    // 检查本地收集数据与服务器数据是否一致
    var localData = ControllerManager.Get<CollectController>().GetCurrUserData();

    // 请求服务器最新数据进行对比
    RequestBeforeResults((serverList) => {
        // 对比逻辑
        bool isConsistent = CompareCollectData(localData, serverList);
        Log.Info($"收集数据一致性检查结果: {isConsistent}");
        return isConsistent;
    });

    return true;
}
```

## 监控和日志

### 1. 增加详细日志

```csharp
private void LogCollectDataOperation(string operation, long[] diceData, ActScoreData scoreData)
{
    Log.Info($"[收集数据操作] {operation} - 骰子结果: [{string.Join(",", diceData)}] - 积分数据: {scoreData}");
}
```

### 2. 数据状态监控

```csharp
public void MonitorCollectDataState()
{
    Log.Info($"[收集数据状态] 待播放队列: {DicePlayingSequence.Count}, 待上报队列: {WaitingSubmitSequence.Count}, 是否已处理: {_isCollectDataProcessed}");
}
```

这些修改将确保收集活动数据的一致性，避免上报失败导致的数据不同步问题。
```
