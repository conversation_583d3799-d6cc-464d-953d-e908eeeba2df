---
description:
globs:
alwaysApply: false
---
# Unity 控制器开发规范

## 继承与获取
- 所有控制器应继承自 [TKController.cs](mdc:Assets/Scripts/GameFrame/TKComponents/MVC/TKController.cs)。
- 控制器统一通过 `ControllerManager.Get<T>()` 获取，见 [ControllerManager.cs](mdc:Assets/Scripts/GameFrame/TKComponents/MVC/Manager/ControllerManager.cs)。

## 命名与结构
- 控制器类名建议以 Controller 结尾。
- 大型控制器建议使用 partial class 拆分。
- 私有字段使用下划线前缀（如 `_fieldName`）。
- 访问其他控制器推荐用 lambda 表达式缓存引用。

## 设计原则
- 控制器应无状态，仅处理业务逻辑。
- 支持多游戏版本时，使用 `#if GAME_DICE`、`#if GAME_SLOT` 等预处理指令区分。

---
详细实现见相关文件。
