#if GAME_DICE
using System;
using System.Collections.Generic;
using CrazyCube.Protobuf.ActivityService;
using CrazyCube.Protobuf.Public;
using CrazyCube.Protobuf.SlotService;
using Cysharp.Threading.Tasks;
using Google.Protobuf;
using Google.Protobuf.Collections;
using MelenitasDev.SoundsGood;
using TKFrame;
using UnityEngine;
using YooAsset;
using static CollectController;

/// <summary>
/// 骰子控制器
/// 负责管理骰子游戏的核心逻辑，包括状态机、网络通信、结果处理等
/// </summary>
public partial class DiceController
{
    /// <summary>
    /// 骰子奖励类型枚举
    /// 定义骰子游戏中可能获得的各种奖励类型
    /// </summary>
    public enum DiceRewardType
    {
        LittleCoin = 1,    // 小金币（部分相同）
        BigCoin = 2,       // 大金币（全部相同）
        Steal = 3,         // 偷取
        Attack = 4,        // 攻击
        Spin = 5,          // 体力
        Shield = 6,        // 盾牌
        Collect = 7,       // 收集
        NormalCoin = 8,    // 普通金币
        Freeroll = 9,      // 免费滚动
        EventIcon = 10,    // 事件图标
    }

    /// <summary>
    /// 引导步骤配置
    /// 定义新手引导中每个步骤的骰子结果，用于教学演示
    /// </summary>
    private static readonly Dictionary<int, long[]> GuideStepResults = new Dictionary<int, long[]>
    {
        { 1, new long[] { (long)SlotId.GoldPakIcon, (long)SlotId.GoldPakIcon, (long)SlotId.SpinsIcon,0 } }, // 步骤1：2金袋+1体力
        { 2, new long[] { (long)SlotId.GoldPakIcon, (long)SlotId.GoldPakIcon, (long)SlotId.GoldPakIcon,0 } }, // 步骤2：3金袋
        { 3, new long[] { (long)SlotId.StealIcon, (long)SlotId.StealIcon, (long)SlotId.StealIcon,0 } }, // 步骤3：完美偷取
        { 4, new long[] { (long)SlotId.GoldIcon, (long)SlotId.ShieldIcon, (long)SlotId.SpinsIcon,0 } }, // 步骤4：1金币+1盾牌+1体力
        { 5, new long[] { (long)SlotId.GoldPakIcon, (long)SlotId.GoldPakIcon, (long)SlotId.AttackIcon,0 } }, // 步骤5：2金袋+1攻击
        { 6, new long[] { (long)SlotId.AttackIcon, (long)SlotId.AttackIcon, (long)SlotId.AttackIcon,0 } }, // 步骤6：完美攻击
        { 7, new long[] { (long)SlotId.GoldIcon, (long)SlotId.GoldIcon, (long)SlotId.ShieldIcon,0 } }, // 步骤7：2金币+1盾牌
        { 8, new long[] { (long)SlotId.ShieldIcon, (long)SlotId.ShieldIcon, (long)SlotId.ShieldIcon,0 } }, // 步骤8：完美盾牌
        { 9, new long[] { (long)SlotId.GoldPakIcon, (long)SlotId.GoldPakIcon, (long)SlotId.GoldPakIcon,0 } }, // 步骤9：3金袋
        { 10, new long[] { (long)SlotId.SpinsIcon, (long)SlotId.SpinsIcon, (long)SlotId.SpinsIcon,0 } }, // 步骤10：完美体力
    };

}

/// <summary>
/// 骰子控制器 - 状态机部分 - 触发事件，用于状态切换
/// 负责管理骰子状态机的各种操作和状态查询
/// </summary>
public partial class DiceController // fsm
{
    /// <summary>
    /// 完成滚动状态
    /// 触发状态机完成滚动事件
    /// </summary>
    public void CompleteRollState()
    {
        _fsm.TriggerEvent(CCRollDiceEventId.CompleteRoll);
    }

    /// <summary>
    /// 自动滚动骰子
    /// 触发状态机自动滚动事件
    /// </summary>
    public void AutoRollDice()
    {
        _fsm.TriggerEvent(CCRollDiceEventId.AutoRoll);
    }

    /// <summary>
    /// 单次滚动骰子
    /// 触发状态机单次滚动事件
    /// </summary>
    public void OnceRollDice()
    {
        _fsm.TriggerEvent(CCRollDiceEventId.OnceRoll);
    }

    /// <summary>
    /// 停止滚动骰子
    /// 触发状态机立即停止滚动事件
    /// </summary>
    public void StopRollDice()
    {
        _fsm.TriggerEvent(CCRollDiceEventId.ImmediatelyStopRoll);
    }

    /// <summary>
    /// 取消滚动骰子
    /// 触发状态机取消滚动事件
    /// </summary>
    public void CancelRollDice()
    {
        _fsm.TriggerEvent(CCRollDiceEventId.CancelRoll);
    }

    /// <summary>
    /// 暂停滚动骰子
    /// 触发状态机暂停滚动事件
    /// </summary>
    public void PauseRollDice()
    {
        _fsm.TriggerEvent(CCRollDiceEventId.PauseRoll);
    }

    /// <summary>
    /// 是否正在滚动
    /// 检查当前状态是否不是空闲状态
    /// </summary>
    public bool IsRolling
    {
        get { return _fsm.CurrentStateName != IdleRollDiceState.NameSingle; }
    }

    /// <summary>
    /// 是否正在自动滚动
    /// 检查当前状态是否为自动滚动相关状态
    /// </summary>
    public bool IsAutoRolling
    {
        get
        {
            Log.ColorInfo($"IsAutoRolling _fsm.CurrentStateName {_fsm.CurrentStateName}");
            return _fsm.CurrentStateName == AutoRollDiceState.NameSingle ||
             _fsm.CurrentStateName == CheckAutoRollDiceState.NameSingle ||
             _fsm.CurrentStateName == PauseRollDiceState.NameSingle;
        }
    }

    /// <summary>
    /// 当前骰子状态名称
    /// </summary>
    public string CurrenDiceState
    {
        get { return _fsm.CurrentStateName; }
    }

    /// <summary>
    /// 是否阻塞任务
    /// 当正在滚动或自动滚动时，某些任务会被阻塞
    /// </summary>
    public bool IsBlockTask => IsRolling || IsAutoRolling;

    /// <summary>
    /// 自动滚动时需要运行的任务类型列表
    /// </summary>
    private List<WaitingTaskType> _runAutoSpinWaitingTask = new List<WaitingTaskType>()
    {
        WaitingTaskType.FriendshipChanged,    // 友谊变化
        WaitingTaskType.UnlockMaxBet,         // 解锁最大倍率
        WaitingTaskType.CoinBlastStartAnim,   // 金币爆炸开始动画
    };
}

/// <summary>
/// 骰子控制器 - 核心数据部分
/// 继承自SlotTaskController，管理骰子游戏的核心数据和状态
/// </summary>
public partial class DiceController : SlotTaskController
{
    /// <summary>
    /// 待播放的骰子队列
    /// 存储从服务器获取但还未播放的骰子结果
    /// </summary>
    public List<long[]> DicePlayingSequence = new List<long[]>();

    /// <summary>
    /// 已播放完等待上报的队列
    /// 存储已经播放完成但还未上报给服务器的骰子结果
    /// </summary>
    public List<long[]> WaitingSubmitSequence = new List<long[]>();

    /// <summary>
    /// 是否正在播放滚动动画
    /// </summary>
    public bool IsRollAnim = false;

    /// <summary>
    /// 当前要播放的骰子面数组
    /// 存储当前正在播放的骰子三个面的结果
    /// </summary>
    public long[] DiceFaceArrayToPlay = new long[3];

    /// <summary>
    /// 骰子状态机实例
    /// </summary>
    private RollDiceFSM _fsm = new RollDiceFSM();

    /// <summary>
    /// 上一次同步金币的时间戳
    /// </summary>
    public long LastSyncCoinsTs = 0;

    /// <summary>
    /// 标记是否需要重新投掷
    /// 当改变SlotBet后，需要调用TryOnceRoll
    /// </summary>
    private bool NeedRoll;

    /// <summary>
    /// 骰子倍率
    /// </summary>
    private int _slotBet;

    /// <summary>
    /// 骰子倍率属性
    /// 设置时会标记需要重新投掷
    /// </summary>
    public int SlotBet
    {
        set
        {
            _slotBet = value;
            NeedRoll = true;
        }
        get
        {
            return _slotBet;
        }
    }

    /// <summary>
    /// 补发的类型
    /// 1攻击 2偷袭
    /// </summary>
    public int RegetKind = (int)SlotId.AttackIcon;

    /// <summary>
    /// 当前投掷的倍数
    /// </summary>
    public int thisTimeSlotBet
    {
        set
        {
            _lastSlotBet = value;
        }
        get
        {
            return _lastSlotBet;
        }
    }

    /// <summary>
    /// 上一次的倍率
    /// </summary>
    private int _lastSlotBet;

    /// <summary>
    /// 任务完成时的处理逻辑
    /// 处理批量投掷相关的任务完成事件
    /// </summary>
    public override async void OnComplete()
    {
        Log.Info($"批量投掷相关 OnComplete");

        // 如果还有待执行的任务，继续执行
        if (_slotTasks.Count > 0)
        {
            ExcuteTask();
            foreach (var item in _slotTasks)
            {
                Log.Info($"[批量投掷相关]OnComplete return TaskType : {item.TaskType} ");
            }
            Log.Info("[批量投掷相关]OnComplete _slotTasks.Count > 0");
            return;
        }

        // 结束各种补发任务
        ControllerManager.Get<AttackController>().TryFinishedAttackReissue(); // 结束攻击补发任务
        ControllerManager.Get<StealController>().TryFinishedStealReissue(); // 结束偷袭补发任务
        //ControllerManager.Get<FreerollController>().TryFinishedFreerollReissue(); // 结束免费转补发任务

        // 触发任务队列
        var taskController = ControllerManager.Get<TaskController>();
        taskController.SetPriorityTaskAndUpdate(_runAutoSpinWaitingTask);

        // 等待优先队列任务执行完毕
        await UniTask.WaitUntil(taskController.IsFinishedRunningPriorityTask);

        // 完成滚动状态并检查引导
        CompleteRollState();
        Log.Info($"批量投掷相关 OnComplete 检查引导{IsRolling}");
        GuideSystem.Instance.CheckAndTriggerGuide();
        ControllerManager.Get<TaskController>().Update();
    }
}

/// <summary>
/// 骰子控制器 - 网络通信部分
/// 负责处理与服务器的网络通信，包括投掷请求、结果上报等
/// </summary>
public partial class DiceController // 网络
{
    /// <summary>
    /// 重连计时器
    /// 用于处理网络重连的计时器
    /// </summary>
    private ulong _reconnectTimer;

    /// <summary>
    /// 上报重连计时器
    /// 用于处理上报超时的计时器
    /// </summary>
    private ulong _timesetReconnectOnUp;

    /// <summary>
    /// 是否正在上报中
    /// 标记当前是否正在向服务器上报投掷结果
    /// </summary>
    private bool IsUping = false;

    /// <summary>
    /// 上报完成后的回调动作
    /// 当上报完成后需要执行的回调函数
    /// </summary>
    private Action UpAction;

    /// <summary>
    /// 本次上报的结果数量
    /// 记录当前批次上报的投掷结果数量
    /// </summary>
    private int hasPreUpCount = 0;

    /// <summary>
    /// 构造函数
    /// 初始化网络回调监听
    /// </summary>
    public DiceController()
    {
        NetworkController networkController = ControllerManager.Get<NetworkController>();
        networkController.AddRecvCallback(WebsocketCommand.SlotList, OnRecieveSlotList);
        networkController.AddRecvCallback(WebsocketCommand.SubSlotList, OnReceiveSubmit);
    }

    /// <summary>
    /// 请求恢复体力
    /// 向服务器发送恢复体力的请求
    /// </summary>
    public void RequestRecoverySpin()
    {
        RecoverySpinRequest request = new RecoverySpinRequest();
        request.Uid = ControllerManager.Get<UserController>().UID;

        // 发送HTTP请求
        GatewayHttpService.Post(HttpServiceRoute.RecoverySpinUrl, request.ToByteArray(), (status, resp) =>
        {
            if (status)
            {
                // 请求成功，解析响应
                var recoverySpinResponse = ProtobufUtil.Parser<RecoverySpinResponse>(resp, () => new RecoverySpinResponse());
                Log.Info($"恢复体力: {recoverySpinResponse} recoverySpinResponse.CurSpin: {recoverySpinResponse.CurSpin}  IsRolling: {IsRolling} Add{recoverySpinResponse.AddSpin}");

                // 触发恢复体力事件
                EventCenter.Instance.Event.TriggerEvent<RecoverySpinResponse>(EventType.SLOT.RECORRY_SPIN, recoverySpinResponse);
            }
            else
            {
                // 请求失败，触发失败事件
                EventCenter.Instance.Event.TriggerEvent<RecoverySpinResponse>(EventType.SLOT.RECORRY_SPIN, null);
            }
        },
        () =>
        {
            // 网络错误，触发失败事件
            EventCenter.Instance.Event.TriggerEvent<RecoverySpinResponse>(EventType.SLOT.RECORRY_SPIN, null);
        });
    }

    /// <summary>
    /// 尝试投掷一次
    /// 核心的投掷方法，处理引导、队列管理和网络请求
    /// </summary>
    public void TryOnceRoll()
    {
        // 检查是否在引导模式中
        if (IsOpenDiceGuideTest && (_currentGuideStep < _guideController.MaxGuideStep))
        {
            Log.Info($"批量投掷相关 TryOnceRoll 引导中");
            StartGuideRoll();
            return;
        }

        Log.Info($"批量投掷相关 TryOnceRoll 待播放队列个数{DicePlayingSequence.Count} 是否直接重请{NeedRoll}");

        // 如果需要重新投掷，则清空待播放队列
        if (NeedRoll)
        {
            DicePlayingSequence.Clear();
            NeedRoll = false;
        }

        if (DicePlayingSequence.Count == 0)
        {
            // 如果当前有待上报的骰子，则需要先上报完成
            if (WaitingSubmitSequence.Count != 0)
            {
                if (_reconnectTimer == 0)
                {
                    Log.Info("批量投掷相关 特殊情况前后端不同步  需要上报所有");
                    Waiting.NotifyShow();
                    _reconnectTimer = Timer.SetInterval(8f, ReconnectAndUpAll);
                }
                else
                {
                    Log.Info("批量投掷相关 正在等待上报回包");
                }
                return;
            }

            // 上报投掷结果后发送新的投掷请求
            SubmitRollResult(SendRoll);
        }
        else // 如果当前有待播放的骰子，则直接播放
        {
            RollOnAnim();
        }
    }

    /// <summary>
    /// 重连并上报所有结果
    /// 只能用在最后一次投掷，打破isuping并重新上报所有，需要配合计时器使用
    /// 用于处理网络异常情况下的数据同步
    /// </summary>
    public void ReconnectAndUpAll()
    {
        Log.Info($"批量投掷相关 自动重连并上报");
        Waiting.NotifyDispose();
        RequestBeforeResults((list) =>
        {
            //现有的待上报序列和后端不一致
            if (WaitingSubmitSequence.Count != list.Count)
            {
                WaitingSubmitSequence = list;
                PreventWebEorror();
                SubmitRollResult(SendRoll);
            }
            else
            {
                for (int i = 0; i < list.Count; i++)
                {
                    if (WaitingSubmitSequence[i] != list[i])
                    {
                        WaitingSubmitSequence = list;
                        PreventWebEorror();
                        SubmitRollResult(SendRoll);
                        return;
                    }
                }

                SendRoll();
            }
        });
    }

    /// <summary>
    /// 发送投掷请求
    /// 清理重连计时器并发送投掷消息
    /// </summary>
    private void SendRoll()
    {
        // 清理重连计时器
        Timer.ClearTimer(_reconnectTimer);
        _reconnectTimer = 0;

        Log.Info("批量投掷相关 发送投掷请求 SendRollmsg");
        SendRollMsg();
        WaitingSubmitSequence.Clear();
    }

    /// <summary>
    /// 发送消息超时计时器
    /// 用于处理发送投掷请求的超时情况
    /// </summary>
    ulong timesetSendMsg = 0;
    /// <summary>
    /// 发送投掷请求
    /// 构建投掷请求消息并发送到服务器，包含测试模式的数据模拟
    /// </summary>
    public void SendRollMsg()
    {
        GetSlotResultListRequest msg = new GetSlotResultListRequest();
        msg.Uid = ControllerManager.Get<UserController>().UID;
        msg.Bet = SlotBet;
        #region 测试模式 - 模拟不同投掷结果
        // 在测试服务器环境下，可以模拟不同的投掷结果进行测试
        if (CGameUrl.curServerType == CGameUrl.goLangTestServer)//需要白名单
        {
            switch (DebugModel.debugSlotNNNId)
            {
                case 0: // 正常随机结果
                    break;
                case 1: // 测试：偷取、攻击、盾牌组合
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 2: // 测试：三个偷取
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    break;
                case 3: // 测试：三个攻击
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    break;
                case 4: // 测试：三个体力
                    msg.Nnn.Add((long)SlotId.SpinsIcon);
                    msg.Nnn.Add((long)SlotId.SpinsIcon);
                    msg.Nnn.Add((long)SlotId.SpinsIcon);
                    break;
                case 5: // 测试：三个盾牌
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 6: // 测试：三个金币
                    msg.Nnn.Add((long)SlotId.GoldIcon);
                    msg.Nnn.Add((long)SlotId.GoldIcon);
                    msg.Nnn.Add((long)SlotId.GoldIcon);
                    break;
                case 7: // 测试：两个金币一个偷取
                    msg.Nnn.Add((long)SlotId.GoldIcon);
                    msg.Nnn.Add((long)SlotId.GoldIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    break;
                case 8: // 测试：一个金币两个偷取
                    msg.Nnn.Add((long)SlotId.GoldIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    break;
                case 9: // 测试：三个金币包
                    msg.Nnn.Add((long)SlotId.GoldPakIcon);
                    msg.Nnn.Add((long)SlotId.GoldPakIcon);
                    msg.Nnn.Add((long)SlotId.GoldPakIcon);
                    break;
                case 10: // 测试：两个金币包一个偷取
                    msg.Nnn.Add((long)SlotId.GoldPakIcon);
                    msg.Nnn.Add((long)SlotId.GoldPakIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    break;
                case 11: // 测试：一个金币包两个偷取
                    msg.Nnn.Add((long)SlotId.GoldPakIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    break;
                case 12: // 测试：收集、偷取、盾牌组合
                    msg.Nnn.Add((long)SlotId.CollectIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 13: // 测试：两个收集一个盾牌
                    msg.Nnn.Add((long)SlotId.CollectIcon);
                    msg.Nnn.Add((long)SlotId.CollectIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 14: // 测试：三个收集
                    msg.Nnn.Add((long)SlotId.CollectIcon);
                    msg.Nnn.Add((long)SlotId.CollectIcon);
                    msg.Nnn.Add((long)SlotId.CollectIcon);
                    break;
                case 15: // 测试：攻击、盾牌、盾牌组合
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 16: // 测试：两个攻击一个盾牌
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    msg.Nnn.Add((long)SlotId.AttackIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 17: // 测试：偷取、盾牌、盾牌组合
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 18: // 测试：两个偷取一个盾牌
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.StealIcon);
                    msg.Nnn.Add((long)SlotId.ShieldIcon);
                    break;
                case 19: // 测试：三个事件图标
                    msg.Nnn.Add((long)SlotId.EventIcon);
                    msg.Nnn.Add((long)SlotId.EventIcon);
                    msg.Nnn.Add((long)SlotId.EventIcon);
                    break;
                case 20: // 测试：三个免费滚动
                    msg.Nnn.Add((long)SlotId.FreeRollIcon);
                    msg.Nnn.Add((long)SlotId.FreeRollIcon);
                    msg.Nnn.Add((long)SlotId.FreeRollIcon);
                    break;
            }
        }
        #endregion
        // 记录当前投注金额
        _lastSlotBet = SlotBet;
        // 发送投掷请求到服务器
        ControllerManager.Get<NetworkController>().SendMsg(WebsocketCommand.SlotList, ByteString.CopyFrom(msg.ToByteArray()), OnSocketDisposed);
        Log.Info($"批量投掷相关 发送Msg: {msg}");

        // 清理之前的超时计时器
        if (timesetSendMsg != 0)
        {
            Timer.ClearTimer(timesetSendMsg);
            timesetSendMsg = 0;
        }

        // 显示等待界面
        Waiting.NotifyShow();
        // 设置6秒超时计时器，防止网络请求无响应
        timesetSendMsg = Timer.SetTimeout(6f, () =>
        {
            Waiting.NotifyDispose();
            StopRollDice();
            Log.Info("批量投掷：结束状态机 重置");
        });
    }

    /// <summary>
    /// 网络连接断开处理
    /// 当网络连接出现问题时自动重连
    /// </summary>
    private void OnSocketDisposed()
    {
        Log.Info("投掷相关 网络问题，重连");
        ControllerManager.Get<NetworkController>().AutoStartReconnect();
    }

    /// <summary>
    /// 收到后端投掷结果回调
    /// 处理服务器返回的投掷结果数据
    /// </summary>
    /// <param name="code">响应状态码，0表示成功</param>
    /// <param name="resp">响应数据</param>
    /// <param name="requestId">请求ID</param>
    void OnRecieveSlotList(int code, byte[] resp, int requestId)
    {
        Waiting.NotifyDispose();
        if (timesetSendMsg != 0)
        {
            Timer.ClearTimer(timesetSendMsg);
            timesetSendMsg = 0;
        }

        if (code == 0)
        {
            // 请求成功，解析响应数据
            var response = ProtobufUtil.Parser<GetSlotResultListResponse>(resp, () => new GetSlotResultListResponse());
            Log.Info($"批量投掷相关 成功收到后端回调 Msg: {response}");
            var results = response.Results;
            NeedRoll = false;
            // 处理服务器返回的投掷结果
            DicePlayingSequence = HandleMessegeData(results);
            // 开始播放投掷动画
            RollOnAnim();
        }
        else
        {
            // 请求失败，重置投注金额并停止投掷
            _lastSlotBet = 0;
            Log.Info($"批量投掷相关 失败收到后端回调 Msg: {code}");
            StopRollDice();
        }
    }

    /// <summary>
    /// 上报投掷结果
    /// 将本地投掷结果上报到服务器，确保数据同步
    /// </summary>
    /// <param name="cb">上报完成后的回调函数</param>
    public void SubmitRollResult(Action cb = null)
    {
        // 检查是否有待上报的结果
        if ((WaitingSubmitSequence == null || WaitingSubmitSequence.Count == 0))
        {
            Log.Info($"批量投掷相关 没有要上报的，将直接执行回调  ");
            cb?.Invoke();
            return;
        }

        // 如果正在上报中，将回调添加到队列中等待上报完成
        if (IsUping)
        {
            Log.Info($"批量投掷相关 正在上报中，将在此次上报结束后继续");
            Action NewCb = UpAction;
            UpAction = () =>
            {
                NewCb?.Invoke();
                SubmitRollResult(cb);
            };
            return;
        }

        // 设置上报完成后的回调
        UpAction = cb;
        var user = ControllerManager.Get<UserController>();
        // 构建上报请求消息
        SubSlotResultRequest msg = new SubSlotResultRequest() { Uid = user.UID };
        // 将本地结果转换为服务器格式
        var result = ReturnToMessegeData(WaitingSubmitSequence);
        for (int i = 0; i < result.Count; i++)
        {
            msg.Results.Add(result[i]);
        }
        // 发送上报请求到服务器
        ControllerManager.Get<NetworkController>().SendMsg(WebsocketCommand.SubSlotList, ByteString.CopyFrom(msg.ToByteArray()));
        // 记录本次上报的结果数量
        hasPreUpCount = result.Count;
        IsUping = true;
        Log.Info("批量投掷相关 准备上报的结果为: " + result.ToString());

        // 增加一个6秒的计时器，防止丢包
        if (_timesetReconnectOnUp == 0)
        {
            _timesetReconnectOnUp = Timer.SetTimeout(6f, () =>
            {
                Log.Info($"批量投掷相关 超过6秒上报未反应，打断重新上报 ");
                PreventWebEorror();
                // 获取服务器端的数据进行对比
                RequestBeforeResults((NetList) =>
                {
                    // 跟后端比较队列，不一致则上报
                    bool isSameList = IsSameUpList(NetList, DicePlayingSequence);
                    Log.Info($"批量投掷相关 后端待消费队列和前台一致吗{isSameList}");
                    IsUping = false;
                    if (!isSameList)
                    {
                        // 数据不一致，重新上报
                        SubmitRollResult();
                    }
                    else
                    {
                        // 数据一致，说明是丢包了，丢弃本地待上报队列
                        WaitingSubmitSequence.Clear();
                    }
                });
            });
        }
    }

    /// <summary>
    /// 收到上报回信
    /// 处理服务器对投掷结果上报的响应
    /// </summary>
    /// <param name="code">响应状态码，0表示成功</param>
    /// <param name="resp">响应数据</param>
    /// <param name="requestId">请求ID</param>
    void OnReceiveSubmit(int code, byte[] resp, int requestId)
    {
        if (!IsUping)
        {
            Log.Info($"批量投掷相关 奇怪的收到了上报回信");
        }

        if (_timesetReconnectOnUp != 0)
        {
            Timer.ClearTimer(_timesetReconnectOnUp);
            _timesetReconnectOnUp = 0;
        }

        IsUping = false;
        if (code == 0)
        {
            // 上报成功，解析响应数据
            var response = ProtobufUtil.Parser<SubSlotResultResponse>(resp, () => new SubSlotResultResponse());
            Log.Info($"批量投掷相关 上报成功 {response} ");

            var data = response.ActData;
            // 移除已成功上报的结果
            for (int i = 0; i < hasPreUpCount; i++)
            {
                Log.Info($"批量投掷相关  移除已上报的结果 {WaitingSubmitSequence[0]} ");
                WaitingSubmitSequence.RemoveAt(0);
            }
            Log.Info($"批量投掷相关 hasPreUpCount {hasPreUpCount}");
            // 执行上报完成后的回调
            UpAction?.Invoke();
        }
        else
        {
            // 上报失败，清空待上报队列并重新获取服务器数据
            Log.Info($"批量投掷相关 hasPreUpCount {hasPreUpCount}  上报失败 ");
            WaitingSubmitSequence.Clear();
            RequestBeforeResults((list) =>
            {
                DicePlayingSequence = list;
                //StopRollDice();
            }, null);
        }
    }

    /// <summary>
    /// 获取后端待消费的序列
    /// 从服务器获取待处理的投掷结果序列，用于数据同步
    /// </summary>
    /// <param name="succCb">成功回调，返回待消费的结果列表</param>
    /// <param name="failCb">失败回调</param>
    public void RequestBeforeResults(Action<List<long[]>> succCb, Action failCb = null)
    {
        GetBeforeResultsRequest getBeforeResultsRequest = new GetBeforeResultsRequest();
        getBeforeResultsRequest.Uid = ControllerManager.Get<UserController>().UID;

        GatewayHttpService.PostWaiting(HttpServiceRoute.GetBeforeResults, getBeforeResultsRequest.ToByteArray(), (Action<bool, byte[]>)((status, resp) =>
        {
            if (status)
            {
                GetBeforeResultsResponse response = ProtobufUtil.Parser<GetBeforeResultsResponse>(resp, () => new GetBeforeResultsResponse());

                List<long[]> tempDiceSequence = HandleMessegeData(response.Results);
                Log.Info($"[DiceController] GetBeforeResults url:{HttpServiceRoute.GetBeforeResults} true response:{response}");
                succCb?.Invoke(tempDiceSequence);
            }
            else
            {
                Error response = ProtobufUtil.Parser<Error>(resp, () => new Error());
                Log.Info($"[DiceController] GetBeforeResults url:{HttpServiceRoute.GetBeforeResults} false response:{response}");
                failCb?.Invoke();
            }
        }), () =>
        {
            failCb?.Invoke();
        });
    }

    /// <summary>
    /// 处理消息数据
    /// 将服务器返回的原始数据转换为骰子结果数组
    /// </summary>
    /// <param name="results">服务器返回的原始数据</param>
    /// <returns>转换后的骰子结果数组列表</returns>
    private List<long[]> HandleMessegeData(RepeatedField<long> results)
    {
        List<long[]> _tempslist = new List<long[]>();
        for (int i = 0; i < results.Count; i++)
        {
            long v = results[i];
            // 解析数据格式：前5位为类型，后5位为数值
            long m = v / 100000;  // 获取类型
            var n = v % 100000;   // 获取数值部分
            _tempslist.Add(new long[] { m, n / 1000, n % 1000 / 10, n % 10 });
        }
        return _tempslist;
    }

    /// <summary>
    /// 将骰子结果数组转换为消息数据
    /// 将本地骰子结果数组转换为服务器可识别的数据格式
    /// </summary>
    /// <param name="longs">骰子结果数组列表</param>
    /// <returns>转换后的消息数据</returns>
    private RepeatedField<long> ReturnToMessegeData(List<long[]> longs)
    {
        RepeatedField<long> _tempsRepeatedField = new RepeatedField<long>();
        for (int i = 0; i < longs.Count; i++)
        {
            // 将数组转换为长整型：类型*100000 + 数值1*1000 + 数值2*10 + 数值3
            long v = longs[i][0] * 100000 + longs[i][1] * 1000 + longs[i][2] * 10 + longs[i][3];
            _tempsRepeatedField.Add(v);
        }
        return _tempsRepeatedField;
    }

    /// <summary>
    /// 执行投掷动画
    /// 处理投掷动画的播放和结果处理
    /// </summary>
    private void RollOnAnim()
    {
        // 记录上一次金币同步的事件戳
        LastSyncCoinsTs = ControllerManager.Get<UserController>().SyncCoinsTs;

        // 暂停投掷状态机
        PauseRollDice();
        // 消耗体力
        _userController.Spins -= _lastSlotBet;
        // 记录体力消耗
        TrackUtil.Consumespins(_lastSlotBet);
        // 获取当前投掷结果
        DiceFaceArrayToPlay = GetCurDiceRusult();
        // 处理奖励类型
        HandleRewardType(DiceFaceArrayToPlay);
        // 触发网络投掷事件
        EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);
        // 上报投掷结果
        SubmitRollResult();
    }

    /// <summary>
    /// 获取当前投掷结果
    /// 从待播放队列中取出一个投掷结果并添加到待上报队列
    /// </summary>
    /// <returns>当前投掷结果数组，如果队列为空则返回null</returns>
    private long[] GetCurDiceRusult()
    {
        // 检查队列是否为空
        if (DicePlayingSequence == null || DicePlayingSequence.Count == 0)
        {
            return null;
        }
        // 取出第一个结果
        var first = DicePlayingSequence[0];
        // 从待播放队列中移除
        DicePlayingSequence.RemoveAt(0);
        // 添加到待上报队列
        WaitingSubmitSequence.Add(first);
        Log.Info($" CurDiceRusult 弹出一个结果 {first[0]} {first[1]} {first[2]} 剩余的长度是{DicePlayingSequence.Count}");
        return first;
    }

    /// <summary>
    /// 防止网络错误
    /// 当网络连接断开时，重置上报状态
    /// </summary>
    public void PreventWebEorror()
    {
        if (IsUping)
        {
            Log.Info($"连接断开，上报失败");
            IsUping = false;
        }
    }

    /// <summary>
    /// 比较两个投掷结果列表是否相同
    /// 用于验证本地数据与服务器数据的一致性
    /// </summary>
    /// <param name="A">第一个投掷结果列表</param>
    /// <param name="B">第二个投掷结果列表</param>
    /// <returns>如果两个列表完全相同则返回true，否则返回false</returns>
    private bool IsSameUpList(List<long[]> A, List<long[]> B)
    {
        // 检查列表长度是否相同
        if (A.Count != B.Count)
        {
            Log.Info($"批量投掷 IsSame 对比序列长度不一致 A的长度为{A.Count} B的长度未{B.Count}");
            return false;
        }
        else
        {
            // 逐个比较每个投掷结果
            for (int i = 0; i < A.Count; i++)
            {
                // 检查数组长度是否相同
                if (A[i].Length != B[i].Length)
                {
                    return false;
                }
                else
                {
                    // 逐个比较数组中的每个元素
                    for (int j = 0; j < A[i].Length; j++)
                    {
                        if (A[i][j] != B[i][j])
                        {
                            Log.Info($"批量投掷 IsSame 当前序号{i} 第一个的值为{A[i].ToString()} 第二个的值为{B[i].ToString()}");
                            return false;
                        }
                    }
                }
            }
            return true;
        }
    }
}

/// <summary>
/// 骰子控制器 - 结果处理部分
/// 负责处理骰子投掷结果的解析和奖励计算
/// </summary>
public partial class DiceController // 结果处理
{
    /// <summary>
    /// 投掷结果结构体
    /// 存储单次投掷的完整结果信息
    /// </summary>
    public struct RollResult
    {
        /// <summary>
        /// 奖励类型
        /// </summary>
        public DiceRewardType RewardType;

        /// <summary>
        /// 获得的金币数量
        /// </summary>
        public long Coins;

        /// <summary>
        /// 获得的体力数量
        /// </summary>
        public long Spins;

        /// <summary>
        /// 获得的盾牌数量
        /// </summary>
        public long Shield;

        /// <summary>
        /// 收集物品数量
        /// </summary>
        public long CollectNum;

        /// <summary>
        /// 是否是全部收集
        /// </summary>
        public bool IsAllCollect;

        /// <summary>
        /// 是否是相同结果
        /// </summary>
        public bool IsSameResult;

        /// <summary>
        /// 骰子结果数组
        /// </summary>
        public RepeatedField<long> Result;

        /// <summary>
        /// 活动数据
        /// </summary>
        public RepeatedField<ActScoreData> ActData;

        /// <summary>
        /// 清空结果数据
        /// 重置所有字段到初始状态
        /// </summary>
        public void Clear()
        {
            RewardType = 0;
            Coins = 0;
            Spins = 0;
            Shield = 0;
            CollectNum = 0;

            if (Result != null)
            {
                Result.Clear();
            }
            else
            {
                Result = new RepeatedField<long>();
            }

            if (ActData != null)
            {
                ActData.Clear();
            }
        }

        /// <summary>
        /// 转换为字符串
        /// 用于调试和日志输出
        /// </summary>
        /// <returns>结果信息的字符串表示</returns>
        public override string ToString()
        {
            return $"RewardType: {RewardType}, Coins: {Coins}, Spins: {Spins}, Shield: {Shield}, CollectNum: {CollectNum}";
        }
    }

    /// <summary>
    /// 当前骰子投掷结果
    /// </summary>
    public RollResult DiceRollResult;

    /// <summary>
    /// 骰子金币配置
    /// </summary>
    private DiceCoinsConfig _diceCoinsConfig => ModelManager.Get<DiceCoinsConfig>();

    /// <summary>
    /// 盾牌上限配置
    /// </summary>
    private List<UpLimitConfig.Item> _shieldLimitConfig => ModelManager.Get<UpLimitConfig>().Shield;

    /// <summary>
    /// 用户控制器
    /// </summary>
    private UserController _userController => ControllerManager.Get<UserController>();

    /// <summary>
    /// 处理奖励类型
    /// 根据骰子面结果计算相应的奖励，按优先级顺序判断奖励类型
    /// </summary>
    /// <param name="diceFaces">骰子三个面的结果数组</param>
    private void HandleRewardType(long[] diceFaces)
    {
        // 清空上一次的投掷结果数据
        DiceRollResult.Clear();

        // 将diceFaces中的每个元素添加到Result中，保存骰子面值
        foreach (var slotId in diceFaces)
        {
            DiceRollResult.Result.Add(slotId);
        }

        // 检查是否所有骰子都是相同面值（相同结果）
        DiceRollResult.IsSameResult = AllDiceType(diceFaces, diceFaces[0]);

        // 按优先级顺序判断奖励类型
        if (AllDiceType(diceFaces, (int)SlotId.SpinsIcon))
        {
            // 所有骰子都是旋转图标 - 奖励旋转次数
            Timer.SetTimeout(1f, () => AudioManager.PlaySound(SoundDef.ui_slot_roll));
            DiceRollResult.RewardType = DiceRewardType.Spin;
            DiceRollResult.Spins = SlotBet * 10; // 体力默认x10倍
        }
        else if (AllDiceType(diceFaces, (long)SlotId.StealIcon))
        {
            // 所有骰子都是偷取图标 - 奖励偷取功能
            // 注释掉的音效代码（可能用于调试）
            // Timer.SetTimeout(0.5f, () =>
            // {
            //     AudioManager.PlaySound(SoundDef.ui_slot_steal);
            // });
            DiceRollResult.RewardType = DiceRewardType.Steal;
        }
        else if (AllDiceType(diceFaces, (long)SlotId.AttackIcon))
        {
            // 所有骰子都是攻击图标 - 奖励攻击功能
            // 注释掉的音效代码（可能用于调试）
            //  Timer.SetTimeout(0.5f, () =>
            //  {
            //     AudioManager.PlaySound(SoundDef.ui_slot_raid);
            // });
            DiceRollResult.RewardType = DiceRewardType.Attack;
        }
        else if (AllDiceType(diceFaces, (long)SlotId.FreeRollIcon))
        {
            // 所有骰子都是免费投掷图标 - 奖励免费投掷
            DiceRollResult.RewardType = DiceRewardType.Freeroll;
        }
        else if (AllDiceType(diceFaces, (long)SlotId.EventIcon))
        {
            // 所有骰子都是事件图标 - 奖励事件功能
            DiceRollResult.RewardType = DiceRewardType.EventIcon;
            Timer.SetTimeout(1f, () =>
            {
                AudioManager.PlaySound(SoundDef.ui_slot_random);
            });
        }
        else if (AllDiceType(diceFaces, (long)SlotId.ShieldIcon))
        {
            // 所有骰子都是盾牌图标 - 奖励盾牌保护
            DiceRollResult.RewardType = DiceRewardType.Shield;
            DiceRollResult.Shield = SlotBet;

            // 计算当前盾牌总数和盾牌上限
            int shieldCount = (int)(ControllerManager.Get<UserController>().Shield + SlotBet);
            int shieldLimitCount = GetShieldLimit();
            Log.Info($"批量投掷相关 盾牌数量{shieldCount} 盾牌上限{shieldLimitCount} SlotBet{SlotBet}");

            // 如果超过盾牌上限，将超出部分转换为旋转次数
            if (shieldLimitCount < shieldCount)
            {
                DiceRollResult.Spins = (shieldCount - shieldLimitCount);
            }
        }
        else if (AllDiceType(diceFaces, (int)SlotId.GoldIcon))
        {
            // 所有骰子都是金币图标 - 奖励普通金币
            DiceRollResult.RewardType = DiceRewardType.NormalCoin;
            DiceRollResult.Coins = ResultCoinNum(diceFaces);
        }
        else if (AllDiceType(diceFaces, (int)SlotId.GoldPakIcon))
        {
            // 所有骰子都是金币包图标 - 奖励大额金币
            DiceRollResult.RewardType = DiceRewardType.BigCoin;
            DiceRollResult.Coins = ResultCoinNum(diceFaces);
        }
        else if (AnyDiceType(diceFaces, (int)SlotId.GoldIcon) || AnyDiceType(diceFaces, (int)SlotId.GoldPakIcon))
        {
            // 部分骰子是金币或金币包图标 - 奖励小额金币
            DiceRollResult.RewardType = DiceRewardType.LittleCoin;
            DiceRollResult.Coins = ResultCoinNum(diceFaces);
        }
        else if (AllDiceType(diceFaces, (int)SlotId.FreeRollIcon))
        {
            // 所有骰子都是免费投掷图标（重复检查，可能是冗余代码）
            DiceRollResult.RewardType = DiceRewardType.Freeroll;
        }

        // 处理收集物奖励
        long collectNum = GetCollectNum(diceFaces, DiceRollResult.RewardType);
        if (collectNum > 0)
        {
            // 检查是否所有骰子都是收集图标（全部收集）
            DiceRollResult.IsAllCollect = AllDiceType(diceFaces, (int)SlotId.CollectIcon);

            // 创建活动分数数据
            ActScoreData actScoreData = new ActScoreData();
            actScoreData.Aid = ControllerManager.Get<CollectController>().GetCurrInfo().Aid;
            actScoreData.ActType = ActType.Collect;
            actScoreData.IncrScore = collectNum * SlotBet;

            // 将活动数据添加到投掷结果中
            DiceRollResult.ActData = new RepeatedField<ActScoreData>();
            DiceRollResult.ActData.Add(actScoreData);

            // 设置收集数量（乘以投注金额）
            DiceRollResult.CollectNum = collectNum * SlotBet;
        }
    }

    /// <summary>
    /// 获取盾牌上限
    /// 根据用户等级获取盾牌上限
    /// </summary>
    /// <returns>盾牌上限</returns>
    public int GetShieldLimit()
    {
        var levelId = ControllerManager.Get<UserController>().LevelId;

        int result = _shieldLimitConfig[0].upLimit;
        for (int i = _shieldLimitConfig.Count - 1; i >= 0; i--)
        {
            var v = _shieldLimitConfig[i];
            if (levelId >= v.level)
            {
                result = v.upLimit;
                break;
            }
        }
        return result;
    }

    /// <summary>
    /// 检查所有骰子面是否都是指定类型
    /// </summary>
    /// <param name="arr">骰子面数组</param>
    /// <param name="type">要检查的类型</param>
    /// <returns>如果所有面都是指定类型返回true，否则返回false</returns>
    public bool AllDiceType(long[] arr, long type)
    {
        if (arr == null)
        {
            return false;
        }
        for (var i = 0; i < 3; i++)
            if (arr[i] != type)
                return false;
        return true;
    }

    /// <summary>
    /// 检查是否有任意一个骰子面是指定类型
    /// </summary>
    /// <param name="diceFaces">骰子面数组</param>
    /// <param name="type">要检查的类型</param>
    /// <returns>如果有任意一个面是指定类型返回true，否则返回false</returns>
    public bool AnyDiceType(long[] diceFaces, long type)
    {
        for (var i = 0; i < 3; i++)
        {
            var face = diceFaces[i];
            if (face == type)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 获取指定类型的骰子面数量
    /// </summary>
    /// <param name="diceFaces">骰子面数组</param>
    /// <param name="type">要统计的类型</param>
    /// <returns>指定类型的骰子面数量</returns>
    public int GetCoinLevel(long[] diceFaces, long type)
    {
        int num = 0;
        if (type == (int)SlotId.GoldPakIcon)
        {
            num = 3;
        }
        for (var i = 0; i < 3; i++)
        {
            var face = diceFaces[i];
            if (face == type)
            {
                num++;
            }
        }
        return num;
    }

    /// <summary>
    /// 计算金币数量
    /// 根据骰子面结果计算相应的金币数量
    /// </summary>
    /// <param name="diceFaces">骰子面数组</param>
    /// <returns>计算后的金币数量</returns>
    private int ResultCoinNum(long[] diceFaces)
    {
        int coinNum = 0;
        Log.Info($"批量投掷 LevelId:{_userController.LevelId}");
        switch (GetFaceNum(diceFaces, (int)SlotId.GoldIcon))
        {
            case 1: coinNum += _diceCoinsConfig.GetCoinNum(DiceCoinsConfig.CoinsKind.OneCoinNum) * SlotBet; break;
            case 2: coinNum += _diceCoinsConfig.GetCoinNum(DiceCoinsConfig.CoinsKind.TwoCoinNum) * SlotBet; break;
            case 3: coinNum += _diceCoinsConfig.GetCoinNum(DiceCoinsConfig.CoinsKind.ThreeCoinNum) * SlotBet; break;
            default:
                break;
        }

        switch (GetFaceNum(diceFaces, (int)SlotId.GoldPakIcon))
        {
            case 1: coinNum += _diceCoinsConfig.GetCoinNum(DiceCoinsConfig.CoinsKind.OneBigCoinNum) * SlotBet; break;
            case 2: coinNum += _diceCoinsConfig.GetCoinNum(DiceCoinsConfig.CoinsKind.TwoBigCoinNum) * SlotBet; break;
            case 3: coinNum += _diceCoinsConfig.GetCoinNum(DiceCoinsConfig.CoinsKind.ThreeBigCoinNum) * SlotBet; break;
            default:
                break;
        }
        //bool isOpenAct = ControllerManager.GetController<ActivityController>().isOpen(ActivityID.CoinBlast);
        //if (isOpenAct)
        //{
        //    var actConfig = ModelManager.GetModel<CoinBlastModel>().BlastData;
        //    LuaArray<LuaObj> list = actConfig["config"];
        //    int betCount = list[0]["bet"];
        //    _coinNum = _coinNum * (1 + betCount);
        //}
        // Log.Info($"ResultCoinNum 计算后的金币数量为 {_coinNum}  金币大爆炸是否开启{isOpenAct}");
        return coinNum;
    }

    /// <summary>
    /// 获取收集数量
    /// 根据骰子面结果计算相应的收集数量
    /// </summary>
    /// <param name="dicefaces">骰子面数组</param>
    /// <param name="rewardType">奖励类型</param>
    /// <returns>收集数量</returns>
    public long GetCollectNum(long[] dicefaces, DiceRewardType rewardType)
    {
        CollectController collectController = ControllerManager.Get<CollectController>();
        if (!collectController.IsCollectAvild())
        {
            Log.Info($"批量投掷相关 收集活动未开启");
            return 0;
        }

        SuperCollecConfigs config = collectController.GetCurrConfig();
        CollectType t = (CollectType)config.ScoreConf.CollectType;
        Log.Info($"收集积分配置{config.ScoreConf}");

        long num = 0;
        if (t == CollectType.Special)
        {
            switch (GetFaceNum(dicefaces, (int)SlotId.CollectIcon))
            {
                case 1: num = config.ScoreConf.OneHit; break;
                case 2: num = config.ScoreConf.TwoHit; break;
                case 3: num = config.ScoreConf.ThreeHit; break;
                default:
                    break;
            }
        }
        else if (t == CollectType.OnlyAttack)
        {
            switch (GetFaceNum(dicefaces, (int)SlotId.AttackIcon))
            {
                case 1: num = config.ScoreConf.OneAttack; break;
                case 2: num = config.ScoreConf.TwoAttack; break;
                case 3: num = config.ScoreConf.AttackSuccess; break;
                default:
                    break;
            }
        }
        else if (t == CollectType.OnlySteal)
        {
            switch (GetFaceNum(dicefaces, (int)SlotId.StealIcon))
            {
                case 1: num = config.ScoreConf.OneSteal; break;
                case 2: num = config.ScoreConf.TwoSteal; break;
                case 3: num = config.ScoreConf.StealSuccess; break;
                default:
                    break;
            }
        }
        else if (t == CollectType.SpecialAttack)
        {
            if (rewardType == DiceRewardType.Attack)
            {
                num = config.ScoreConf.AttackSuccess;
            }
            else
            {
                switch (GetFaceNum(dicefaces, (int)SlotId.CollectIcon))
                {
                    case 1: num = config.ScoreConf.OneHit; break;
                    case 2: num = config.ScoreConf.TwoHit; break;
                    case 3: num = config.ScoreConf.ThreeHit; break;
                    default:
                        break;
                }
            }
        }
        else if (t == CollectType.SpecialSteal)
        {
            if (rewardType == DiceRewardType.Steal)
            {
                num = config.ScoreConf.StealSuccess;
            }
            else
            {
                switch (GetFaceNum(dicefaces, (int)SlotId.CollectIcon))
                {
                    case 1: num = config.ScoreConf.OneHit; break;
                    case 2: num = config.ScoreConf.TwoHit; break;
                    case 3: num = config.ScoreConf.ThreeHit; break;
                    default:
                        break;
                }
            }
        }
        else if (t == CollectType.SpecialAttackAndSteal)
        {
            if (rewardType == DiceRewardType.Attack)
            {
                num = config.ScoreConf.AttackSuccess;
            }
            else if (rewardType == DiceRewardType.Steal)
            {
                num = config.ScoreConf.StealSuccess;
            }
            else
            {
                switch (GetFaceNum(dicefaces, (int)SlotId.CollectIcon))
                {
                    case 1: num = config.ScoreConf.OneHit; break;
                    case 2: num = config.ScoreConf.TwoHit; break;
                    case 3: num = config.ScoreConf.ThreeHit; break;
                    default:
                        break;
                }
            }
        }

        return num;
    }

    /// <summary>
    /// 获取指定类型的骰子面数量
    /// </summary>
    /// <param name="diceFaces">骰子面数组</param>
    /// <param name="type">要统计的类型</param>
    /// <returns>指定类型的骰子面数量</returns>
    public int GetFaceNum(long[] diceFaces, int type)
    {
        int num = 0;
        for (var i = 0; i < 3; i++)
        {
            var face = diceFaces[i];
            if (face == type)
            {
                num++;
            }
        }
        return num;
    }
}

/// <summary>
/// 骰子控制器 - 场景管理部分
/// 负责骰子游戏场景的加载、关闭和皮肤切换功能
/// 管理骰子场景的生命周期和资源加载
/// </summary>
public partial class DiceController // 场景
{
    /// <summary>
    /// 骰子场景对象
    /// 存储已加载的骰子场景GameObject引用
    /// </summary>
    private GameObject _diceSceneObj = null;

    /// <summary>
    /// 是否正在加载场景
    /// 防止重复加载场景的标记
    /// </summary>
    private bool _isLoading = false;

    /// <summary>
    /// 待处理的回调函数
    /// 在场景加载过程中保存的回调，加载完成后统一处理
    /// </summary>
    private Action<GameObject> _pendingCallback = null;

    /// <summary>
    /// 加载骰子场景
    /// 异步加载骰子游戏场景，支持回调处理
    /// </summary>
    /// <param name="cb">场景加载完成后的回调函数</param>
    public void LoadDiceScene(Action<GameObject> cb)
    {
        // 如果场景已经加载，直接激活并回调
        if (_diceSceneObj != null)
        {
            _diceSceneObj.SetActive(true);
            cb?.Invoke(_diceSceneObj);
            return;
        }

        // 如果正在加载中，保存回调等待加载完成
        if (_isLoading)
        {
            // 如果正在加载，将回调保存起来，等加载完成后统一处理
            _pendingCallback += cb;
            return;
        }

        // 开始加载场景
        _isLoading = true;
        var DiceSceneNode = GameObject.Find("DiceSceneNode");
        if (DiceSceneNode == null)
        {
            // 创建场景节点，设置位置
            DiceSceneNode = new GameObject("DiceSceneNode");
            DiceSceneNode.transform.position = new Vector3(300, 0, 0);
        }

        // 设置场景节点不被销毁
        GameObject.DontDestroyOnLoad(DiceSceneNode);
        _ = ResUtil.LoaDefaultdPref("Dice_DiceScene", DiceSceneNode.transform, (obj) =>
        {
            // 场景加载完成后的处理
            _diceSceneObj = obj;
            _isLoading = false;

            // 处理所有待处理的回调
            if (_pendingCallback != null)
            {
                _pendingCallback.Invoke(_diceSceneObj);
                _pendingCallback = null;
            }

            // 处理当前回调
            cb?.Invoke(_diceSceneObj);
        });
    }

    /// <summary>
    /// 关闭骰子场景
    /// 隐藏骰子场景对象，但不销毁资源
    /// </summary>
    public void CloseDiceScene()
    {
        if (_diceSceneObj != null)
        {
            _diceSceneObj.SetActive(false);
        }
    }

    #region  切换骰子皮肤

    /// <summary>
    /// 在主场景中切换骰子皮肤
    /// 异步加载皮肤纹理并应用到材质上
    /// </summary>
    /// <param name="obj">骰子对象</param>
    /// <param name="matr">骰子材质</param>
    /// <param name="skinId">皮肤ID</param>
    /// <returns>异步任务</returns>
    public async UniTask ChangeDiceSkinInMainScene(GameObject obj, Material matr, long skinId)
    {
        // 特殊皮肤ID处理：清除纹理
        if (skinId == 5001)
        {
            matr.SetTexture("_BaseTex", null);
            return;
        }

        Log.Info($"DiceController.ChangeDiceSkin: skinId={skinId}");
        // 异步加载皮肤纹理
        var op = YooAssets.LoadAssetAsync<Texture2D>($"DiceSkinTexture_Skin{skinId}");
        await op.ToUniTask().AttachExternalCancellation(obj.GetCancellationTokenOnDestroy());
        var sp = op.AssetObject as Texture2D;
        op.Release();
        // 应用纹理到材质
        matr.SetTexture($"_BaseTex", sp);
    }

    /// <summary>
    /// 在UI中切换骰子皮肤
    /// 异步加载皮肤材质并应用到MeshRenderer
    /// </summary>
    /// <param name="obj">骰子对象</param>
    /// <param name="meshRenderer">网格渲染器</param>
    /// <param name="skinId">皮肤ID</param>
    /// <returns>异步任务</returns>
    public async UniTask ChangeDiceSkinInUI(GameObject obj, MeshRenderer meshRenderer, long skinId)
    {
        {
            Log.Info($"DiceController.ChangeDiceSkin: skinId={skinId}");
            // 异步加载皮肤材质
            var op = YooAssets.LoadAssetAsync<Material>($"DiceSkinMatiral_Skin{skinId}");
            await op.ToUniTask().AttachExternalCancellation(obj.GetCancellationTokenOnDestroy());
            var sp = op.AssetObject as Material;
            op.Release();
            // 应用材质到网格渲染器
            meshRenderer.material = sp;
        }
    }
    #endregion

}

/// <summary>
/// 骰子控制器 - 引导投掷部分
/// 负责新手引导中的骰子投掷逻辑，包括引导步骤管理和测试功能
/// 处理引导过程中的骰子结果和奖励发放
/// </summary>
public partial class DiceController // 引导投掷
{
    /// <summary>
    /// 引导控制器引用
    /// 用于访问引导相关的功能和数据
    /// </summary>
    private GuideController _guideController => ControllerManager.Get<GuideController>();

    /// <summary>
    /// 测试步骤计数器
    /// 用于调试和测试引导功能
    /// </summary>
    long _testStep = 0;

    /// <summary>
    /// 是否开启骰子引导测试
    /// 控制引导测试功能的开关
    /// </summary>
    public bool IsOpenDiceGuideTest = true;

    /// <summary>
    /// 设置骰子引导测试开关
    /// 从本地存储加载引导测试设置并应用到控制器
    /// </summary>
    public void SetIsOpenDiceGuideTest()
    {
        var IsOpenDiceGuideTest = SaveUtils.Load<bool>(StorageKey.DebugKey.IsOpenDiceGuideTest, true);
        ControllerManager.Get<DiceController>().IsOpenDiceGuideTest = IsOpenDiceGuideTest;
    }

    /// <summary>
    /// 当前引导步骤
    /// 获取和设置当前新手引导的步骤进度
    /// </summary>
    private long _currentGuideStep
    {
        get
        {
            // 返回测试步骤（调试用）
            // return _testStep;
            if (_guideController?.UserNoviceGuideData != null)
            {
                return _guideController.UserNoviceGuideData.SlotStep;
            }
            return 0;
        }
        set
        {
            // 设置测试步骤（调试用）
            // _testStep=value;
            // return;
            if (_guideController?.UserNoviceGuideData != null)
            {
                _guideController.UserNoviceGuideData.SlotStep = value;
            }
        }
    }

    /// <summary>
    /// 是否正在引导投掷
    /// 标记当前是否正在进行引导投掷过程
    /// </summary>
    private bool _isGuideRolling = false;

    /// <summary>
    /// 开始引导投掷
    /// 启动新手引导中的骰子投掷流程
    /// </summary>
    public void StartGuideRoll()
    {
        // 检查是否已经在引导投掷中（已注释）
        // if (_isGuideRolling)
        // {
        //     Log.Error("Guide roll is already in progress");
        //     return;
        // }

        _isGuideRolling = true;
        RollOnAnimOnGuide();
        _guideController.RequestFinishSlotNoviceGuideStep();
    }

    /// <summary>
    /// 在引导中执行骰子动画
    /// 处理引导投掷的骰子结果和奖励逻辑
    /// </summary>
    private void RollOnAnimOnGuide()
    {
        // 暂停正常投掷，进入引导模式
        PauseRollDice();
        // 消耗体力
        _userController.Spins -= 1;
        // 增加引导步骤
        _currentGuideStep++;
        // 设置测试步骤（调试用）
        //_currentGuideStep=11;
        Log.Info($"批量投掷 引导投掷 当前步骤: {_currentGuideStep}");

        // 获取引导步骤的结果
        long[] result = GetGuideStepResult(_currentGuideStep);
        if (result == null)
        {
            Log.Error($"Invalid guide step: {_currentGuideStep}");
            return;
        }

        // 设置骰子结果并播放动画
        DiceFaceArrayToPlay = result;
        HandleRewardType(DiceFaceArrayToPlay);

        // 处理引导奖励：如果不是攻击或偷取类型，且有配置的奖励
        if (DiceRollResult.RewardType != DiceRewardType.Attack &&
            DiceRollResult.RewardType != DiceRewardType.Steal && _guideController?.SlotNoviceGuideConfs != null &&
            _currentGuideStep > 0 &&
            _currentGuideStep <= _guideController.SlotNoviceGuideConfs.Count &&
            _guideController.SlotNoviceGuideConfs[(int)_currentGuideStep - 1].Prize != null &&
            _guideController.SlotNoviceGuideConfs[(int)_currentGuideStep - 1].Prize.Count > 0 &&
            _guideController.SlotNoviceGuideConfs[(int)_currentGuideStep - 1].Prize[0].PropsId == PropID.Coin  // 1表示金币类型
           )
        {
            // 设置引导奖励的金币数量
            DiceRollResult.Coins = _guideController.SlotNoviceGuideConfs[(int)_currentGuideStep - 1].Prize[0].Num;
        }

        // 触发网络骰子投掷事件
        EventCenter.Instance.Event.TriggerEvent(EventType.Dice.NetRollDice);
    }

    /// <summary>
    /// 获取引导步骤结果
    /// 根据引导步骤获取预设的骰子结果
    /// </summary>
    /// <param name="step">引导步骤</param>
    /// <returns>骰子结果数组，如果步骤无效则返回null</returns>
    private long[] GetGuideStepResult(long step)
    {
        if (GuideStepResults.TryGetValue((int)step, out long[] result))
        {
            return result;
        }
        return null;
    }
}

#endif
