﻿using CrazyCube.Protobuf.Public;
using Google.Protobuf;
using TKFrame;

public partial class HeartbeatController
{
    //心跳间隔时间
    public int IntervalTime { get; set; }

    //心跳超时时间
    public int TimeOut { get; set; }

    //最近一次发送心跳包时间
    public long LastSendHeartbeatTime { get; set; }

    //最近一次收到心跳包时间
    public long LastReceiveHeartbeatTime { get; set; }

    //RTT(Round-Trip Time):即时往返时延
    public double RTT;

    //RTT(Round-Trip Time):平均往返时延
    public double AverageRTT;

    //统计RTT次数
    public int countRTT;

    //累计RTT时间
    public double sumRTT;

    //心跳机制开始时间
    public long StartTime;

    //心跳机制结束时间
    public long EndTime;

    //定时器ID
    private ulong timerId;

    //重连次数
    private int RetryCount = 0;
}

/// <summary>
/// 心跳系统
/// </summary>
public partial class HeartbeatController : TKController
{
    /// <summary>
    /// 初始化
    /// </summary>
    public HeartbeatController()
    {
        IntervalTime = 10;//心跳间隔时间
        TimeOut = 8;//心跳超时时间
        LastSendHeartbeatTime = -1;
        LastReceiveHeartbeatTime = -1;
        RetryCount = 0;

        NetworkController networkController = ControllerManager.Get<NetworkController>();
        networkController.AddRecvCallback(WebsocketCommand.SessionHeartbeat, OnHeartbeatMessage);
    }

    /// <summary>
    /// 开始发送
    /// </summary>
    public void Start()
    {
        //记录心跳开始时间
        StartTime = GameSystemManager.GetInstance().GetCurrentClientTime();

        RetryCount = 0;
        //启动心跳定时器
        timerId = Timer.SetInterval(IntervalTime, UpdateHeartbeat);
    }

    /// <summary>
    /// 更新心跳系统
    /// </summary>
    public void UpdateHeartbeat()
    {
        if (IsDisposed()) return;

        //异常时将主动断开网络连接
        if (!CheckHeartbeatTimeout())
        {
            Log.Info($"HeartbeatController UpdateHeartbeat RetryCount {RetryCount}");
            if (RetryCount > 0)
            {
                RetryCount--;
            }
            else
            {
                Log.Info("HeartbeatController UpdateHeartbeat Close AutoStartReconnect");
                ControllerManager.Get<NetworkController>().AutoStartReconnect();//自动重连
                return;
            }
        }

        //定时发送心跳消息
        long nowTime = GameSystemManager.GetInstance().GetCurrentClientTime();
        if (nowTime - LastSendHeartbeatTime > IntervalTime - 1)
        {
            if (ControllerManager.Get<NetworkController>().IsConnected)
            {
                SendHeartbeatMsg(); // 仅在网络连接时发送心跳
            }
            //Log.Info($"HeartbeatController UpdateHeartbeat {ControllerManager.Get<NetworkController>().IsConnected}");

            LastSendHeartbeatTime = nowTime;
        }
    }

    /// <summary>
    /// 检查心跳是否超时
    /// </summary>
    /// <returns></returns>
    private bool CheckHeartbeatTimeout()
    {
        double nowTime = GameSystemManager.GetInstance().GetCurrentClientTime();

        //开始计时
        if (LastSendHeartbeatTime > 0)
        {
            if (LastReceiveHeartbeatTime < 0 && nowTime - LastSendHeartbeatTime > TimeOut) //发送心跳包后，没有收到回包
            {
                Log.Info($"HeartbeatController timeout LastReceiveHeartbeatTime：{LastReceiveHeartbeatTime} time: {nowTime - LastSendHeartbeatTime}");
                return false;
            }
        }

        //Log.Info($"HeartbeatController CheckHeartbeatTimeout {ControllerManager.Get<NetworkController>().IsConnected}");
        return ControllerManager.Get<NetworkController>().IsConnected ? true : false;
    }

    /// <summary>
    /// 发送心跳消息
    /// </summary>
    private void SendHeartbeatMsg()
    {
        //发送心跳包
        ControllerManager.Get<NetworkController>().SendMsg(WebsocketCommand.SessionHeartbeat, ByteString.Empty);

        LastSendHeartbeatTime = GameSystemManager.GetInstance().GetCurrentClientTime();
        LastReceiveHeartbeatTime = -1;
    }

    /// <summary>
    /// 同步最后一次接收消息时间
    /// </summary>
    public void SyncLastReceiveMsgTime(long timestamp)
    {
        LastReceiveHeartbeatTime = GameSystemManager.GetInstance().GetCurrentClientTime();
        //Log.Info($"HeartbeatController SyncLastReceiveMsgTime LastReceiveHeartbeatTime {LastReceiveHeartbeatTime}");
        RetryCount = 0;
        if (timestamp > 0)
        {
            long serverTime = timestamp / 1000;//服务器当前时间（单位秒）
            GameSystemManager.GetInstance().SyncServerTime(serverTime);
        }
    }

    /// <summary>
    /// 接收心跳消息
    /// </summary>
    /// <param name="code"></param>
    /// <param name="msg"></param>
    /// <param name="requestId"></param>
    private void OnHeartbeatMessage(int code, byte[] msg, int requestId)
    {
        if (code == 0)
        {
            RetryCount = 0;
            LastReceiveHeartbeatTime = GameSystemManager.GetInstance().GetCurrentClientTime();
        }
    }

    /// <summary>
    /// 关闭心跳系统
    /// </summary>
    public void Close()
    {
        //停止心跳定时器
        Timer.ClearTimer(timerId);

        //停止记录时间
        LastSendHeartbeatTime = -1;
        LastReceiveHeartbeatTime = -1;

        //记录心跳结束时间
        EndTime = GameSystemManager.GetInstance().GetCurrentClientTime();
        Log.Info($"HeartbeatController Close {EndTime}");
    }

    public override void Dispose()
    {
        NetworkController networkController = ControllerManager.Get<NetworkController>();
        networkController.RemoveRecvCallback(WebsocketCommand.SessionHeartbeat, OnHeartbeatMessage);
        base.Dispose();
    }
}