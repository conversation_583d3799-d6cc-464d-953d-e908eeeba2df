---
description: 
globs: 
alwaysApply: false
---
# Unity 数据与配置规范

## 数据获取
- **配置模型**: 业务配置和数据模型统一通过 `ModelManager.Get<T>()` 获取，例如 `ModelManager.Get<GameBasicConfig>()`。
- **只读性**: 配置模型在运行时应为只读，不应在业务逻辑中修改。

## 配置文件
- **格式**: 配置文件统一使用 JSON 格式。
- **解析**: 使用 LitJson 库（`JsonData`）进行 JSON 解析，例如 `JsonMapper.ToObject(text)`。

## 数据缓存与持久化
- **标准接口**: 使用 `SaveUtils` 工具类进行本地数据持久化。
- **存储类型**:
  - `SaveUtils.Save<T>() / Load<T>()`: 用于通用数据的存取。
  - `SaveUtils.SaveUser<T>() / LoadUser<T>()`: 用于与特定用户无关的本地化数据。
  - `SaveUtils.SaveRemoteUser() / LoadRemoteUser()`: 用于与用户UID关联的数据。
  - `SaveUtils.SaveToFile() / LoadFromFile()`: 用于将数据序列化到指定文件。
- **键名管理**: 持久化使用的Key统一在 `StorageKey` 类中管理。

## 典型用法
- 获取模型：`var userModel = ModelManager.Get<UserModel>();`
- 解析配置：`JsonData config = JsonMapper.ToObject(text);`

---
详细实现见 `ModelManager` 和 `SaveUtils` 相关代码。


